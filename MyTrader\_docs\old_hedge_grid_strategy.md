# Hedge Grid Strategy Description

This document outlines the concept and logic for the Hedge Grid trading strategy.

## Overview

The strategy aims to profit from price volatility in perpetual futures markets (specifically BTC/USDT initially). It operates under the assumption that the exchange account is configured for **Hedge Mode (BothSides)**, allowing simultaneous long and short positions for the same contract.

## Core Logic

1.  **Step-Based Entries:**
    *   Define a fixed price interval (`StepSize`, e.g., 100 USDT).
    *   Define a fixed position size (`PositionSizePerStep`, e.g., 0.01 BTC).
    *   When the market price moves `StepSize` away from the price level of the last opened step, open **two** new positions:
        *   One long position of `PositionSizePerStep`.
        *   One short position of `PositionSizePerStep`.
    *   Record the price level at which this step was initiated.

2.  **Step Profit Taking:**
    *   Monitor the price movement relative to the entry price of *each* individual step.
    *   If the current price moves `StepSize` *above* the entry price of a specific step:
        *   Close the long portion (size `PositionSizePerStep`) associated with *that specific step* to realize profit (e.g., 0.1 USDT for the example parameters).
        *   The short portion of that step remains open.
    *   If the current price moves `StepSize` *below* the entry price of a specific step:
        *   Close the short portion (size `PositionSizePerStep`) associated with *that specific step* to realize profit.
        *   The long portion of that step remains open.

3.  **Position Accumulation:**
    *   During trending market conditions, one side (long or short) will consistently have its profit taken, while the other side accumulates, resulting in a growing aggregate position (e.g., multiple open short steps in an uptrend) with increasing unrealized loss.

4.  **Large Hedge Mechanism (Risk Mitigation):**
    *   **Trigger:** Continuously monitor the account's margin status and proximity to liquidation.
    *   When the position nears a predefined liquidation risk threshold (e.g., `HedgeTriggerLiquidationRatio` = 50% of the way to the calculated liquidation price):
        *   Identify the side with the large accumulated losing position (long or short).
        *   Open a single, larger position in the **opposite** direction (the "hedge position").
        *   **Hedge Size:** The size of this hedge position needs to be calculated dynamically (e.g., `HedgeSizeFactor` = 2x the total size of the accumulated losing steps) to ensure its potential PnL can sufficiently offset the losses of the step positions if the trend continues.
    *   **Hedge Management:**
        *   If the price continues in the *favorable* direction for the hedge (unfavorable for the step positions), the hedge PnL grows, counteracting the step losses.
        *   If the price reverses *against* the hedge, the hedge position should potentially be closed quickly at a minimal loss and reopened if the trend resumes, to avoid accumulating losses on the hedge itself (this incurs fees and slippage).

5.  **Consolidation / Reset:**
    *   Define conditions under which the entire set of positions (accumulated steps + hedge) should be closed.
    *   **Trigger:** This could be when the positive PnL from the hedge position sufficiently offsets the negative PnL from the accumulated steps (e.g., `ConsolidationProfitTarget` = Net PnL >= 0), or based on other risk metrics.
    *   **Action:** Close *all* open long positions and *all* open short positions (including the large hedge).
    *   Reset the strategy state to begin the step process anew.

## Key Parameters

*   `StepSize`: The price distance between grid levels (e.g., 100 USDT).
*   `PositionSizePerStep`: The size of the long/short position opened at each step (e.g., 0.001 BTC).
*   `HedgeTriggerLiquidationRatio`: Threshold for activating the large hedge (e.g., 0.5).
*   `HedgeSizeFactor`: Multiplier to determine the size of the large hedge relative to accumulated losing steps (e.g., 2.0).
*   `ConsolidationProfitTarget`: Net PnL target for closing all positions after hedge activation (e.g., 0 USDT).

## Assumptions & Considerations

*   Runs on a single account/API key.
*   Exchange supports Hedge Mode (BothSides).
*   Sufficient capital exists to manage margin for accumulating positions and the large hedge.
*   Trading fees, funding rates, and slippage will impact profitability and need to be considered in backtesting/simulation.
*   Accurate calculation of liquidation price and margin requirements in hedge mode is critical.
*   Parameter tuning is essential for performance. 
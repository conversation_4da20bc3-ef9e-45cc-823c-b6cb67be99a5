---
description: Project Coding Rules
globs: 
alwaysApply: false
---
# Mandatory Coding Rules

This document defines mandatory engineering rules for all code in this project. These rules are critical for correctness, maintainability, and simulation accuracy.

---

## 1. Asynchronous Task Handling

**Fire-and-forget tasks are strictly forbidden.**  
You must never start an async operation without a way to observe its completion and exceptions.

**Forbidden:**
```csharp
_ = SomeAsyncMethod();
Task.Run(() => SomeMethod());
```

**Allowed:**
```csharp
Task backgroundTask = SomeAsyncMethod(); // You can await, check, or catch exceptions later
```
You may assign a background task to a variable (`anyTask = ...`) as long as you can later await or check its result.

**Rationale:**  
- Uncatchable exceptions, debugging complexity, resource leaks, and sequencing issues.

---

## 2. Order Execution Price Source

**Always use the exact Bid (`HighestBid`) and Ask (`LowestAsk`) prices from market data for order execution and matching.**  
Never use Mark Price or any synthetic/approximate price for order matching. This applies to both spot and futures simulation.

**Correct:**
```csharp
if (order.Side == OrderSide.Buy && data.LowestAsk <= executionPrice) { ... }
if (order.Side == OrderSide.Sell && data.HighestBid >= executionPrice) { ... }
```

**Incorrect:**
```csharp
if (order.Side == OrderSide.Buy && data.MarkPrice <= executionPrice) { ... }
```

**Rationale:**  
- Using the wrong price source leads to unrealistic fills and invalid backtesting.

---


**Violations of these rules are considered critical bugs.**
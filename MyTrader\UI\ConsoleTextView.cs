using System;
using System.IO;
using System.Text;
using Terminal.Gui;

namespace MyTraderSpace.UI
{
    /// <summary>
    /// A TextView that can be used as console output redirection target
    /// </summary>
    public class ConsoleTextView : TextView
    {
        private readonly ConsoleTextWriter _writer;
        
        /// <summary>
        /// Creates a new ConsoleTextView
        /// </summary>
        public ConsoleTextView()
        {
            _writer = new ConsoleTextWriter(this);
            Text = string.Empty;
            ReadOnly = true;
        }

        public bool IsScrollToEndEnabled
        {
            get => _writer.IsScrollToEndEnabled;
            set => _writer.IsScrollToEndEnabled = value;
        }

        /// <summary>
        /// Gets the TextWriter that can be used with Console.SetOut
        /// </summary>
        public TextWriter Writer => _writer;
        
        /// <summary>
        /// Scrolls the text view to display the latest content, regardless of IsScrollToEndEnabled.
        /// Use this for manual scroll triggers.
        /// </summary>
        public void ScrollToEnd()
        {
            // Ensure this runs on the main loop for thread safety with UI updates
            Application.MainLoop?.Invoke(() =>
            {
                if (Lines > 0 && Bounds.Height > 0) // Check Bounds.Height
                {
                    // Calculate the line that should be at the top
                    int topVisibleLine = Math.Max(0, Lines - Bounds.Height);
                    ScrollTo(topVisibleLine); // ScrollTo sets the TopRow
                }
            });
        }

        /// <summary>
        /// A TextWriter implementation that redirects to a TextView
        /// </summary>
        private class ConsoleTextWriter : TextWriter
        {
            private readonly ConsoleTextView _textView;
            private readonly StringBuilder _buffer = new StringBuilder();
            private readonly object _bufferLock = new object();
            private const int FLUSH_INTERVAL_MS = 200; // Flush 5 times a second
            private object? _timerToken; // To manage the timer, returned by AddTimeout

            public ConsoleTextWriter(ConsoleTextView textView)
            {
                _textView = textView;
                if (Application.MainLoop != null)
                {
                    _timerToken = Application.MainLoop.AddTimeout(TimeSpan.FromMilliseconds(FLUSH_INTERVAL_MS), FlushBufferScheduled);
                }
                else
                {
                    Console.Error.WriteLine("ConsoleTextWriter: MainLoop not available during construction, buffer flushing will not occur.");
                }
            }

            private bool FlushBufferScheduled(MainLoop mainLoop) // Method signature matches MainLoop.AddTimeout
            {
                if (_textView == null || !_textView.Visible || !_textView.IsInitialized) // Check if textView is still valid, visible and initialized
                {
                    _timerToken = null; // Timer will stop
                    return false; 
                }

                string contentToFlush;
                lock (_bufferLock)
                {
                    if (_buffer.Length == 0)
                    {
                        return true; // Keep timer running, nothing to flush
                    }
                    contentToFlush = _buffer.ToString();
                    _buffer.Clear();
                }

                int? originalTopRow = null;
                if (!_textView.IsScrollToEndEnabled)
                {
                    originalTopRow = _textView.TopRow;
                }

                _textView.Text += contentToFlush;

                if (originalTopRow.HasValue)
                {
                    _textView.ScrollTo(originalTopRow.Value);
                }
                else if (IsScrollToEndEnabled)
                {
                    _textView.ScrollToEnd();
                }
                return true; // Keep timer running
            }

            public override Encoding Encoding => Encoding.UTF8;

            public bool IsScrollToEndEnabled { get; set; } = true;

            public override void Write(char value)
            {
                lock (_bufferLock)
                {
                    _buffer.Append(value);
                }
            }

            public override void Write(string? value)
            {
                if (string.IsNullOrEmpty(value)) return;
                lock (_bufferLock)
                {
                    _buffer.Append(value);
                }
            }

            public override void WriteLine(string? value)
            {
                lock (_bufferLock)
                {
                    if (!string.IsNullOrEmpty(value))
                    {
                        _buffer.Append(value);
                    }
                    _buffer.AppendLine();
                }
            }

            protected override void Dispose(bool disposing)
            {
                if (disposing)
                {
                    if (_timerToken != null && Application.MainLoop != null)
                    {
                        Application.MainLoop.RemoveTimeout(_timerToken);
                        _timerToken = null;
                    }
                    // Attempt to flush any remaining buffer content if the main loop is still available
                    if (Application.MainLoop != null)
                    {
                         // We are in Dispose, so we need to be careful. 
                         // The FlushBufferScheduled itself checks if _textView is valid.
                         // Calling it directly here might be okay if the MainLoop is still processing events.
                        string contentToFlush = string.Empty;
                        lock (_bufferLock)
                        {
                            if (_buffer.Length > 0)
                            {
                                contentToFlush = _buffer.ToString();
                                _buffer.Clear();
                            }
                        }
                        if (!string.IsNullOrEmpty(contentToFlush) && _textView != null && _textView.IsInitialized && _textView.Visible)
                        {
                            // Directly update if possible, or invoke if not on main thread (though Dispose should be)
                            // This is tricky territory in Dispose.
                            _textView.Text += contentToFlush; 
                            if (IsScrollToEndEnabled)
                                _textView.ScrollToEnd();
                        }
                    }
                }
                base.Dispose(disposing);
            }
        }
    }
}

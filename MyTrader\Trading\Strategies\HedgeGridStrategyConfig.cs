using MyTraderSpace.Logging;
using MyTraderSpace.Models; // For TimeInForce, OrderType

namespace MyTraderSpace.Trading.Strategies
{
    public class HedgeGridStrategyConfig
    {
        public LogLevel LogLevel { get; set; } = LogLevel.Information; // Default log level
        // Symbol is inherited from BaseExchangeAPI.TradingPair

        public decimal StepSize { get; set; } = 100m; // e.g., 100 USDT
        public decimal PositionSizePerStep { get; set; } = 0.01m; // e.g., 0.01 BTC
        public decimal ProfitTargetDistance { get; set; } = 200m; // e.g., 200 USDT from entry
        public decimal Leverage { get; set; } = 100m; // Default leverage, e.g., 100x

        public TimeInForce BaseOrderTimeInForce { get; set; } = TimeInForce.FillOrKill;
        public TimeInForce TakeProfitOrderTimeInForce { get; set; } = TimeInForce.GoodTillCancel;
        public OrderType BaseOrderType { get; set; } = OrderType.Limit;
        public OrderType TakeProfitOrderType { get; set; } = OrderType.Limit;
        public decimal BaseOrderLimitPriceAdjustment { get; set; } = 1.0m; // e.g., 1 USDT adjustment to limit price

        // Ratio of liquidation price to mark price that triggers a stop for this strategy instance
        public decimal StopOnLiquidationPriceRatio { get; set; } = 0.8m; // e.g., stop if 80% towards liquidation

        // Tolerance for price deviation when making trade decisions (e.g., reopening, initial placement)
        public decimal MaxInitialStepSpreadTolerancePercentage { get; set; } = 0.0002m; // 0.02% default
    }
}
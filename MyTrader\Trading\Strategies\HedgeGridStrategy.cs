using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;
using MyTraderSpace.Utils;

namespace MyTraderSpace.Trading.Strategies
{
    public class HedgeGridStrategy : BaseStrategy
    {
        private readonly LogManager _log;
        private readonly HedgeGridStrategyConfig _strategyConfig;
        public decimal IntendedPrice { get; private set; } = 0m;
        private FuturesMarketData? _latestFuturesData;
        private Task _processingTask = Task.CompletedTask;
        private PositionModelUpdate? _currentLongPosition;  // so far this is just for the UI (might be removed in the future)
        private PositionModelUpdate? _currentShortPosition; // so far this is just for the UI (might be removed in the future)
        public record FetchedPositionsResult(PositionModel? LongPosition, PositionModel? ShortPosition);
        private const int MaxRecentErrors = 5;
        private readonly LimitedConcurrentQueue<string> _recentErrors = new LimitedConcurrentQueue<string>(MaxRecentErrors); // TODO: don't forget to subscribe/receive the errors from the OrderSides
        private readonly object _positionLock = new object();
        private readonly bool _isInitial = false;

        public OrderPair LongSide { get; private set; }
        public OrderPair ShortSide { get; private set; }

        /// <summary>
        /// Indicates if the strategy is valid (IntendedPrice != 0) and in Ready state. (Reconstruction was successful)
        /// </summary>
        public bool IsValid => State == StrategyState.Ready && IntendedPrice != 0; // marks if reconstruction was successful
        public bool IsAnySideAwaitingResult => (LongSide?.IsAwaitingResult ?? false) || (ShortSide?.IsAwaitingResult ?? false);

        /// <summary>
        /// A strategy is 'Active' if it has an open position on either side (Long or Short) or if the base order is filled on either side (in this case the corresponding position also has to be open!)
        /// Note: Only Not Active sub-strategies can be removed when the priced moved more than StepSize away (pruneds) from the "last frontier" sub-strategy price
        /// </summary>
        /// <returns>bool</returns>
        public bool IsActive()
        {
            var positions = FetchPositionsAsync().Result; // This is a blocking call, consider refactoring to async if needed, or it could be enoguh just checking against ShortSide and LongSide
            return ((LongSide?.IsBaseOrderFilled ?? false) && (ShortSide?.IsBaseOrderFilled ?? false)) || ((positions.LongPosition?.AveragePrice != 0) && (positions.ShortPosition?.AveragePrice != 0));
        }
        // Make sure we can be only once 'truly' first time filled both base orders and never again
        private bool _internal_IsWasBothBaseOrdersFilledFirstTime = false; // this can be true only once during the lifetime of the strategy! (and never false again)
        public bool IsWasBothBaseOrdersFilledFirstTime { get; private set; } = false; // this can be true only once during the lifetime of the strategy! (and never false again)

        public decimal AccumulatedReportedRealizedPnL { get; private set; } = 0m;
        public decimal AccumulatedCalculatedRealizedPnL { get; private set; } = 0m;
        public decimal PeakStrategyCalculatedRealizedPnL { get; private set; } = 0m;
        public decimal PeakStrategyReportedRealizedPnL { get; private set; } = 0m;
        public decimal TroughStrategyCalculatedRealizedPnL { get; private set; } = decimal.MaxValue;
        public decimal TroughStrategyReportedRealizedPnL { get; private set; } = decimal.MaxValue;
        public Fee AccumulatedReportedFees { get; private set; } = new Fee();
        public Fee AccumulatedCalculatedFees { get; private set; } = new Fee();

        public event Action<HedgeGridStrategy>? OnRemovalRequest;
        public event Action<FuturesMarketData>? OnMarketDataUpdate;
        public event Action<HedgeGridStrategy, OrderPair>? OnBaseOrderPairFilled;
        public event Action<HedgeGridStrategy, OrderPair>? OnTakeProfitOrderPairFilled;
        public event Action<HedgeGridStrategy, OrderPair, OrderResult>? OnStrategyError; // Forward errors to MainStrategy

        /// <summary>
        /// 
        /// </summary>
        /// <param name="nameId"></param>
        /// <param name="intendedPrice"></param>
        /// <param name="exchangeAPI"></param>
        /// <param name="marketDataService"></param>
        /// <param name="config"></param>
        /// <param name="isInitialBlankSlateStep">If this is the "First Special Case" (Blank Slate) meaning the very first sub-strategy point (no previous points, and no points from reconstruction either)</param>
        /// <exception cref="ArgumentNullException"></exception>
        public HedgeGridStrategy(string nameId, decimal intendedPrice, BaseExchangeAPI exchangeAPI, IMarketDataService marketDataService, HedgeGridStrategyConfig config, bool isInitialBlankSlateStep = false)
            : base(nameId, exchangeAPI, marketDataService)
        {
            _isInitial = isInitialBlankSlateStep; // Store if this is the very first step on a blank slate
            _log = new LogManager($"{nameof(HedgeGridStrategy)}-{nameId.Split('_').LastOrDefault() ?? nameId}", config.LogLevel);
            _strategyConfig = config ?? throw new ArgumentNullException(nameof(config));
            IntendedPrice = intendedPrice; // This might be 0 if MainStrategy expects reconstruction to set it, or a target price for a new step
            AccumulatedReportedFees = new Fee(ExchangeAPI.TradingPair);
            AccumulatedCalculatedFees = new Fee(ExchangeAPI.TradingPair);
            _log.Information($"[CONSTRUCTOR] HedgeGridStrategy '{NameId}' created. InitialIntendedPrice: {IntendedPrice}, IsInitialBlankSlate: {_isInitial}, Symbol: {Symbol}.");
        }

        public override async Task InitializeAsync()
        {
            if (State != StrategyState.Initializing)
            {
                _log.Warning($"[{NameId}] InitializeAsync called but state is {State}. Returning.");
                return;
            }

            _log.Information($"[INIT] Initializing HedgeGridStrategy '{NameId}'...");
            try
            {
                if (_strategyConfig == null || _exchangeAPI == null || _marketDataService == null)
                {
                    _log.Error($"[{NameId}] StrategyConfig, ExchangeAPI, or MarketDataService is null. Cannot initialize.");
                    State = StrategyState.Error;
                    return;
                }

                // Pass _isInitial to OrderPair constructor.
                // If this HGS is for a blank slate (_isInitial = true), OrderPair will use config for first order types/TIF.
                // Otherwise (reconstruction or subsequent new step), OrderPair will use PostOnly/Limit.
                LongSide = new OrderPair("LongSide", true, ExchangeAPI.TradingPair, ExchangeAPI.GetFeeRates(), _strategyConfig, _log, _isInitial);
                ShortSide = new OrderPair("ShortSide", false, ExchangeAPI.TradingPair, ExchangeAPI.GetFeeRates(), _strategyConfig, _log, _isInitial);
                
                // Set OrderPair IntendedPrice before reconstruction attempts if this HGS is for a *new* (non-blank-slate) step.
                // If _isInitial is true, or IntendedPrice is 0, ReConstructStateAsync will try to set it from exchange.
                if (!_isInitial && IntendedPrice > 0)
                {
                    LongSide.IntendedPrice = IntendedPrice;
                    ShortSide.IntendedPrice = IntendedPrice;
                    _log.Information($"[{NameId}] Pre-setting OrderPair IntendedPrice to {IntendedPrice} as this is a non-initial step.");
                }

                await ReConstructStateAsync(); // Attempts to find existing state and set/confirm IntendedPrice

                // After ReConstructStateAsync, IntendedPrice should be non-zero if reconstruction was successful OR if it was a valid new step.
                // NOTE: ReConstructions doesn't mean sure reconstructed state. If the _exchangeAPI doesn't return fetched positions and/or orders, means we are in blank state
                // TODO: This needs to be deiced and reconsciled!!
                if (IntendedPrice <= 0)
                {
                    if (_isInitial)
                    {
                        _log.Warning($"[{NameId}] InitializeAsync: This is an initial blank slate step and IntendedPrice is still 0 after ReConstructStateAsync. This HGS is not yet valid and relies on MainStrategy to set a price or provide conditions for it.");
                        // State remains Initializing. MainStrategy must handle this (e.g. by setting IntendedPrice later and calling a method to complete setup, or by discarding this HGS).
                        // This HGS instance is NOT ready for trading yet.
                        // **************
                        // Important Note: We return here in still Initializing State, so "outside" has to be taken into account this, and not ideally we can expect HGS-es in Initializing state even after Initialization (might not be too wise this)
                        // Note2: Seems like okay, as in both 2 places where InitializeAsync is called in MainStrategy (InitializeAsync and CreateAndActivateNewHedgeGridStepAsync), after that an IsValid() check is done, and if that fails, it will be properly deconstructed
                        // **************
                        return; 
                    }
                    else
                    {
                         _log.Error($"[{NameId}] InitializeAsync: IntendedPrice is 0 after reconstruction for a non-initial step. This indicates a failure to reconstruct a valid state from the exchange or a missing intended price from MainStrategy.");
                         State = StrategyState.Error;
                         return;
                    }
                }

                // If IntendedPrice is now valid, ensure OrderPairs have it (might have been set during reconstruction).
                LongSide.IntendedPrice = IntendedPrice;
                ShortSide.IntendedPrice = IntendedPrice;

                SubscribeToOrderPairEvents(LongSide);
                SubscribeToOrderPairEvents(ShortSide);
                SubscribeToExchangeEvents();

                _log.Information($"[{NameId}] HedgeGridStrategy initialized successfully. Final IntendedPrice: {IntendedPrice}. IsInitialBlankSlate: {_isInitial}.");
                State = StrategyState.Ready;

                // For a truly new step (_isInitial=true and now Ready with a valid IntendedPrice), 
                // StartAsync will handle the initial order placement via StartStepOrders.
                // For reconstructed steps, ValidateStrategyState (called by market updates) will manage necessary order placements (e.g., missing TPs).
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[INIT] Error during HedgeGridStrategy '{NameId}' initialization.");
                State = StrategyState.Error;
                // Ensure OrderPairs are disposed if created before exception
                (LongSide as IDisposable)?.Dispose(); 
                (ShortSide as IDisposable)?.Dispose();
            }
        }

        private void SubscribeToOrderPairEvents(OrderPair orderPair)
        {
            ArgumentNullException.ThrowIfNull(orderPair);
            orderPair.OnOrderPlacementRequest += HandleOrderPlacementRequest;
            orderPair.OnBaseOrderFilled += HandleBaseOrderPairFilled;
            orderPair.OnTakeProfitOrderFilled += HandleTakeProfitOrderPairFilled;
            orderPair.OnError += HandleOrderPairError;
        }

        private void UnsubscribeFromOrderPairEvents(OrderPair orderPair)
        {
            ArgumentNullException.ThrowIfNull(orderPair);
            orderPair.OnOrderPlacementRequest -= HandleOrderPlacementRequest;
            orderPair.OnBaseOrderFilled -= HandleBaseOrderPairFilled;
            orderPair.OnTakeProfitOrderFilled -= HandleTakeProfitOrderPairFilled;
            orderPair.OnError -= HandleOrderPairError;
        }

        private async Task<FetchedPositionsResult> FetchPositionsAsync()
        {
            try
            {
                var positions = await _exchangeAPI.GetPositionsAsync(Category.Linear, Symbol);
                var longPosition = positions.FirstOrDefault(p => (p.Direction == PositionDirection.Buy || p.Side == Models.PositionSide.Buy) && p.Quantity != 0);
                var shortPosition = positions.FirstOrDefault(p => (p.Direction == PositionDirection.Sell || p.Side == Models.PositionSide.Sell) && p.Quantity != 0);
                _log.Debug($"[FETCH POSITIONS] '{NameId}' successfully fetched positions. Long: {longPosition?.Quantity}, Short: {shortPosition?.Quantity}");
                return new FetchedPositionsResult(longPosition, shortPosition);
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[FETCH POSITIONS] '{NameId}' failed to fetch open positions from exchange.");
                throw;
            }
        }

        private async Task<IEnumerable<OrderModel>> FetchOrdersAsync()
        {
            try
            {
                var orders = await _exchangeAPI.GetActiveOrdersForCategoryAsync(Category.Linear, Symbol);
                _log.Debug($"[FETCH ORDERS] '{NameId}' successfully fetched {orders.Count()} active orders for {Symbol} from exchange.");
                return orders;
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[FETCH ORDERS] '{NameId}' failed to fetch active orders from exchange.");
                throw;
            }
        }

        private async Task ReConstructStateAsync()
        {
            _log.Information($"[{NameId}] ReConstructStateAsync: Attempting to reconstruct state. Current HGS IntendedPrice: {IntendedPrice}, IsInitial: {_isInitial}");

            var positionsResult = await FetchPositionsAsync();
            var activeOrders = (await FetchOrdersAsync()).ToList();

            OrderModelUpdate? longBaseRecon = null;
            OrderModelUpdate? longTpRecon = null;
            OrderModelUpdate? shortBaseRecon = null;
            OrderModelUpdate? shortTpRecon = null;

            decimal reconLongPrice = 0m;
            decimal reconShortPrice = 0m;
            decimal reconLongQty = 0m;
            decimal reconShortQty = 0m;

            // --- Try to Reconstruct Long Side ---
            if (positionsResult.LongPosition != null && positionsResult.LongPosition.Quantity != 0)
            {
                _log.Information($"[{NameId}] Recon: Found existing LONG position (Qty: {positionsResult.LongPosition.Quantity}, AvgPx: {positionsResult.LongPosition.AveragePrice}). Treating as filled base.");
                longBaseRecon = positionsResult.LongPosition.ToOrderModelUpdate();
                reconLongPrice = longBaseRecon.AveragePrice ?? 0m;
                reconLongQty = longBaseRecon.QuantityFilled ?? 0m;

                longTpRecon = activeOrders.FirstOrDefault(o =>
                    o.Side == Models.OrderSide.Sell &&
                    o.IsReduceOnly &&
                    (o.PositionDirection == PositionDirection.Buy || o.PositionDirection == PositionDirection.OneWay) &&
                    o.Quantity == reconLongQty &&
                    IsTpOrderPlausible(o, reconLongPrice, true) 
                )?.ToOrderModelUpdate();
                if (longTpRecon != null)
                    _log.Information($"[{NameId}] Recon: Matched active order {longTpRecon.ClientOrderId ?? longTpRecon.OrderId} as LongSide TP.");
            }
            else 
            {
                var pendingLongBases = activeOrders.Where(o => o.Side == Models.OrderSide.Buy && !o.IsReduceOnly && o.PositionDirection == Models.PositionDirection.Buy).ToList();
                if (pendingLongBases.Count == 1)
                {
                    longBaseRecon = pendingLongBases.First().ToOrderModelUpdate();
                    reconLongPrice = longBaseRecon.Price ?? 0m; 
                    reconLongQty = longBaseRecon.Quantity;
                    _log.Information($"[{NameId}] Recon: Found PENDING long base order {longBaseRecon.ClientOrderId ?? longBaseRecon.OrderId}. Px: {reconLongPrice}, Qty: {reconLongQty}");
                }
                else if (pendingLongBases.Count > 1)
                {
                    _log.Warning($"[{NameId}] Recon: Found {pendingLongBases.Count} pending long base orders. Ambiguous. Will not reconstruct LongSide from pending orders.");
                }
            }
            LongSide.ReconstructOrders(longBaseRecon, longTpRecon);

            // --- Try to Reconstruct Short Side ---
            if (positionsResult.ShortPosition != null && positionsResult.ShortPosition.Quantity != 0)
            {
                _log.Information($"[{NameId}] Recon: Found existing SHORT position (Qty: {positionsResult.ShortPosition.Quantity}, AvgPx: {positionsResult.ShortPosition.AveragePrice}). Treating as filled base.");
                shortBaseRecon = positionsResult.ShortPosition.ToOrderModelUpdate();
                reconShortPrice = shortBaseRecon.AveragePrice ?? 0m;
                reconShortQty = shortBaseRecon.QuantityFilled ?? 0m;

                shortTpRecon = activeOrders.FirstOrDefault(o =>
                    o.Side == Models.OrderSide.Buy &&
                    o.IsReduceOnly &&
                    (o.PositionDirection == PositionDirection.Sell || o.PositionDirection == PositionDirection.OneWay) &&
                    o.Quantity == reconShortQty &&
                     IsTpOrderPlausible(o, reconShortPrice, false) 
                )?.ToOrderModelUpdate();
                if (shortTpRecon != null) _log.Information($"[{NameId}] Recon: Matched active order {shortTpRecon.ClientOrderId ?? shortTpRecon.OrderId} as ShortSide TP.");
            }
            else 
            {
                var pendingShortBases = activeOrders.Where(o => o.Side == Models.OrderSide.Sell && !o.IsReduceOnly && o.PositionDirection == Models.PositionDirection.Sell).ToList();
                if (pendingShortBases.Count == 1)
                {
                    shortBaseRecon = pendingShortBases.First().ToOrderModelUpdate();
                    reconShortPrice = shortBaseRecon.Price ?? 0m; 
                    reconShortQty = shortBaseRecon.Quantity;
                    _log.Information($"[{NameId}] Recon: Found PENDING short base order {shortBaseRecon.ClientOrderId ?? shortBaseRecon.OrderId}. Px: {reconShortPrice}, Qty: {reconShortQty}");
                }
                else if (pendingShortBases.Count > 1)
                {
                    _log.Warning($"[{NameId}] Recon: Found {pendingShortBases.Count} pending short base orders. Ambiguous. Will not reconstruct ShortSide from pending orders.");
                }
            }
            ShortSide.ReconstructOrders(shortBaseRecon, shortTpRecon);

            // --- Determine HGS IntendedPrice --- 
            // Priority: 1. Both bases filled, 2. One base filled, 3. Both bases pending, 4. One base pending, 5. Pre-set by MainStrategy (if new step), 6. Blank slate (remains 0)
            if (longBaseRecon?.Status == OrderStatus.Filled && shortBaseRecon?.Status == OrderStatus.Filled && reconLongPrice > 0 && reconShortPrice > 0)
            { 
                IntendedPrice = (reconLongPrice + reconShortPrice) / 2m;
                _log.Information($"[{NameId}] Recon: Both sides filled. HGS IntendedPrice: {IntendedPrice} (from LongAvgPx: {reconLongPrice}, ShortAvgPx: {reconShortPrice}).");
            }
            else if (longBaseRecon?.Status == OrderStatus.Filled && reconLongPrice > 0)
            { 
                IntendedPrice = reconLongPrice;
                _log.Information($"[{NameId}] Recon: Only Long side filled. HGS IntendedPrice: {IntendedPrice} (from LongAvgPx).");
            }
            else if (shortBaseRecon?.Status == OrderStatus.Filled && reconShortPrice > 0)
            { 
                IntendedPrice = reconShortPrice;
                _log.Information($"[{NameId}] Recon: Only Short side filled. HGS IntendedPrice: {IntendedPrice} (from ShortAvgPx).");
            }
            else if (longBaseRecon != null && shortBaseRecon != null && reconLongPrice > 0 && reconShortPrice > 0) // Both have pending orders
            { 
                IntendedPrice = (reconLongPrice + reconShortPrice) / 2m;
                _log.Information($"[{NameId}] Recon: Both sides PENDING. HGS IntendedPrice: {IntendedPrice} (avg of L_Px: {reconLongPrice}, S_Px: {reconShortPrice}).");
            }
            else if (longBaseRecon != null && reconLongPrice > 0) // Only Long pending
            { 
                 IntendedPrice = reconLongPrice;
                _log.Information($"[{NameId}] Recon: Only Long PENDING. HGS IntendedPrice: {IntendedPrice} (from LongPendingPx).");
            }
             else if (shortBaseRecon != null && reconShortPrice > 0) // Only Short pending
            { 
                IntendedPrice = reconShortPrice;
                _log.Information($"[{NameId}] Recon: Only Short PENDING. HGS IntendedPrice: {IntendedPrice} (from ShortPendingPx).");
            }
            else if (IntendedPrice > 0 && !_isInitial) // Was set by MainStrategy for a new (non-blank-slate) step, and no exchange state found.
            {
                _log.Information($"[{NameId}] Recon: No reconstructible state on exchange. Using pre-set HGS IntendedPrice: {IntendedPrice} (for new, non-blank-slate step).");
            }
            else if (_isInitial && IntendedPrice == 0) // Blank slate, no state found, HGS IntendedPrice was 0 from MainStrategy.
            {
                _log.Information($"[{NameId}] Recon: Initial Blank Slate step, no exchange state found, HGS IntendedPrice remains 0. MainStrategy/market data will drive actual price setting.");
            }
            else 
            {
                _log.Warning($"[{NameId}] Recon: Could not determine a definitive IntendedPrice from exchange state or pre-set values. HGS IntendedPrice remains {IntendedPrice}. This HGS might not become Ready if IntendedPrice is 0.");
            }
            
            if (IntendedPrice > 0)
            {
                LongSide.IntendedPrice = IntendedPrice;
                ShortSide.IntendedPrice = IntendedPrice;
            }

            _log.Information($"[{NameId}] ReConstructStateAsync: Finished. Final HGS IntendedPrice: {IntendedPrice}.");
        }

        private bool IsTpOrderPlausible(OrderModel order, decimal baseEntryPrice, bool isBaseLong)
        {
            if (order.Price == null || baseEntryPrice <= 0) return false;
            decimal tpPrice = order.Price.Value;
            if (isBaseLong)
            {
                return tpPrice > baseEntryPrice * (1 - _strategyConfig.MaxInitialStepSpreadTolerancePercentage); 
            }
            else 
            {
                return tpPrice < baseEntryPrice * (1 + _strategyConfig.MaxInitialStepSpreadTolerancePercentage); 
            }
        }

        public override async Task StartAsync()
        {
            if (State == StrategyState.Running)
            {
                _log.Warning($"[{NameId}] StartAsync called but already running.");
                return;
            }
            if (State != StrategyState.Ready)
            {
                 _log.Error($"[{NameId}] StartAsync: Strategy not Ready. State: {State}. Cannot start.");
                 return;
            }
            // IntendedPrice must be set to a valid value before StartAsync can proceed to Running.
            if (IntendedPrice <= 0)
            {
                _log.Error($"[{NameId}] StartAsync: IntendedPrice is not valid ({IntendedPrice}). Cannot start. This should have been handled during InitializeAsync.");
                State = StrategyState.Error; // Or remain Ready but non-operational
                // **************
                // Important NOTE: Handle the cases "outside" when a StartAsync was called but the state resulted in Error (or morelike not Running!)
                // **************
                return;
            }

            _log.Information($"[{NameId}] Starting HedgeGridStrategy (IntendedPrice: {IntendedPrice}, IsInitialBlankSlate: {_isInitial})...");

            if (!await ValidateExchangeState()) 
            {
                _log.Error($"[{NameId}] StartAsync: Exchange state is invalid. Cannot start strategy.");
                State = StrategyState.Error;
                // **************
                // Important NOTE: Handle the cases "outside" when a StartAsync was called but the state resulted in Error (or morelike not Running!)
                // **************
                return;
            }

            State = StrategyState.Running;

            if (_isInitial && IntendedPrice > 0) 
            {
                _log.Information($"[{NameId}] Initial blank slate step is Running. Attempting initial base orders via StartStepOrders.");
                StartStepOrders();
            }
            else
            {
                 _log.Information($"[{NameId}] Reconstructed or subsequent new step is Running. ValidateStrategyState will manage orders based on market data.");
                 // ValidateStrategyState(); // Optional: immediate check. Otherwise, market data will trigger it.
            }

            _log.Information($"[{NameId}] HedgeGridStrategy Started and is now in Running state.");
        }

        public override async Task StopAsync()
        {
            if (State == StrategyState.Stopped || State == StrategyState.Stopping)
            {
                _log.Warning($"[{NameId}] StopAsync called but already stopping/stopped.");
                return;
            }
            _log.Information($"[{NameId}] Stopping HedgeGridStrategy...");
            State = StrategyState.Stopping;
            UnsubscribeFromExchangeEvents();
            UnsubscribeFromOrderPairEvents(LongSide);
            UnsubscribeFromOrderPairEvents(ShortSide);
            _log.Information($"[{NameId}] HedgeGridStrategy stopped.");
            State = StrategyState.Stopped;
            await Task.CompletedTask;
        }

        // Important Note: this is the good use and implementation. The reason is, that although we can get complete/full response from ExchangeAPI.OpenFuturesPositionAsync immediately,
        // ExchangeAPI still sends out the OrderUpdate and/or PositionUpdate events, leading us to double-result, and just confusion.
        // This means better stick always to the results received from OnOrderUpdate and/or OnPositionUpdate events
        private async void HandleOrderPlacementRequest(OrderPair sender, FuturesOrderRequest request)
        {
            _log.Information($"Strategy handling placement request for ClientID: {request.ClientId} (Name: {sender.Name})");
            OrderResult? result = null;
            OrderModelUpdate? updateToForward = null;
            try
            {
                result = await ExchangeAPI.OpenFuturesPositionAsync(request).ConfigureAwait(false);
                updateToForward = CreateOrderModelUpdateFromResult(result, request);
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Exception during API call for ClientID {request.ClientId}.");
                string errorMessage = $"API Call Fail ClientID {request.ClientId}: {ex.Message}";
                _recentErrors.Enqueue(errorMessage);
                updateToForward = CreateFailedOrderModelUpdate(request, $"API Exception: {ex.Message}");
            }

            if (updateToForward != null && sender != null)
            {
                sender.HandleOrderUpdate(updateToForward);
            }
            else
                throw new Exception("HandleOrderPlacementRequest: Not Suppose to Happen: updateToForward or Sender is null");
        }

        private void HandleOrderPairError(OrderPair sender, OrderResult errorResult)
        {
            _log.Error($"[{NameId}] ({sender.Name}): Error: {errorResult.Message}");
            _recentErrors.Enqueue($"[{NameId}] ( {sender.Name} ): Error: {errorResult.Message}");
            _log.Debug($"Error details: ClientOrderId={errorResult.ClientOrderId}, Status={errorResult.Status}, Success={errorResult.IsSuccess}");
            OnStrategyError?.Invoke(this, sender, errorResult);
        }

        private void HandleBaseOrderPairFilled(OrderPair sender)
        {
            _log.Information($"[{NameId}] ({sender.Name}): Base order filled. Cloid: {sender.BaseOrder?.ClientOrderId}, OID: {sender.BaseOrder?.OrderId}, AvgPx: {sender.BaseOrder?.AveragePrice}");
            
            if (sender == LongSide && LongSide.IsBaseOrderFilled)
            {
                _log.Information($"[{NameId}] LongSide base filled. Requesting TP placement.");
                // Note: We should be able to this here, but this will be also called on the next ticker data event's ValidateStrategyState call, so hopefully things are propely guarded with the IsAwaitingResult flag
                LongSide.RequestTakeProfitOrderPlacement();
            }
            else if (sender == ShortSide && ShortSide.IsBaseOrderFilled)
            {
                _log.Information($"[{NameId}] ShortSide base filled. Requesting TP placement.");
                // Note: We should be able to this here, but this will be also called on the next ticker data event's ValidateStrategyState call, so hopefully things are propely guarded with the IsAwaitingResult flag
                ShortSide.RequestTakeProfitOrderPlacement();
            }

            // Note: Let's just forward this in partial filled case as well, so MainStrategy if nothing else, can do a log about (so we can have more clarity in one log file)
            // and MainStrategy can do the if (LongSide.IsBaseOrderFilled && ShortSide.IsBaseOrderFilled) itself
            _log.Debug($"[{NameId}] Base Order ({sender.Name}) filled for IntendedPrice: {IntendedPrice}. Signaling MainStrategy.");
            if (LongSide.IsBaseOrderFilled && ShortSide.IsBaseOrderFilled)
            {
                if (_internal_IsWasBothBaseOrdersFilledFirstTime == false)
                {
                    _internal_IsWasBothBaseOrdersFilledFirstTime = true; // will be set true only once, never again
                    IsWasBothBaseOrdersFilledFirstTime = true;
                }
                else // if was true, IsWasBothBaseOrdersFilledFirstTime will not be true ever again
                {
                    IsWasBothBaseOrdersFilledFirstTime = false;
                    _log.Debug($"[{NameId}] Both base orders filled. IsWasBothBaseOrdersFilled set to true.");
                }
            }
            UpdateAccumulatedPnlAndFees(sender);
            OnBaseOrderPairFilled?.Invoke(this, sender);
        }

        private void HandleTakeProfitOrderPairFilled(OrderPair sender)
        {
            _log.Information($"[{NameId}] ({sender.Name}): TP filled. Cloid: {sender.TakeProfitOrder?.ClientOrderId}, OID: {sender.TakeProfitOrder?.OrderId}, AvgPx: {sender.TakeProfitOrder?.AveragePrice}, PnL: {sender.CalculatedRealizedPnL}");
            UpdateAccumulatedPnlAndFees(sender);
            
            OnTakeProfitOrderPairFilled?.Invoke(this, sender); // forward to MainStrategy
        }

        // TODO: Check if this is *fully* *complete* and correct!
        private void UpdateAccumulatedPnlAndFees(OrderPair sender)
        {
            if (sender.CalculatedRealizedPnL.HasValue)
            {
                AccumulatedCalculatedRealizedPnL += sender.CalculatedRealizedPnL.Value;
                if (AccumulatedCalculatedRealizedPnL > PeakStrategyCalculatedRealizedPnL)
                    PeakStrategyCalculatedRealizedPnL = AccumulatedCalculatedRealizedPnL;
                if (TroughStrategyCalculatedRealizedPnL == decimal.MaxValue || AccumulatedCalculatedRealizedPnL < TroughStrategyCalculatedRealizedPnL)
                    TroughStrategyCalculatedRealizedPnL = AccumulatedCalculatedRealizedPnL;
            }
            if (sender.CalcFees.HasValue)
                AccumulatedCalculatedFees += sender.CalcFees.Value;

            if (sender.ReportedRealizedPnL.HasValue)
            {
                AccumulatedReportedRealizedPnL += sender.ReportedRealizedPnL.Value;
                if (AccumulatedReportedRealizedPnL > PeakStrategyReportedRealizedPnL)
                    PeakStrategyReportedRealizedPnL = AccumulatedReportedRealizedPnL;
                if (TroughStrategyReportedRealizedPnL == decimal.MaxValue || AccumulatedReportedRealizedPnL < TroughStrategyReportedRealizedPnL)
                    TroughStrategyReportedRealizedPnL = AccumulatedReportedRealizedPnL;
            }
            if (sender.ReportedFees.HasValue)
                AccumulatedReportedFees += sender.ReportedFees.Value;

            _log.Information($"[{NameId}] AGGREGATE PnL: AccumulatedCalcPnL: {AccumulatedCalculatedRealizedPnL:F4}, AccumulatedReportedPnL: {AccumulatedReportedRealizedPnL:F4}, AccumulatedRepFees: {AccumulatedReportedFees.Quote.Amount:F4}, AccumulatedCalcFees: {AccumulatedCalculatedFees.Quote.Amount:F4}");
        }

        public void StartStepOrders()
        {
            if (State != StrategyState.Ready && State != StrategyState.Running) // Question: also 'Ready' state qualifys as valid state to StartStepOrders ?!
            {
                _log.Warning($"[{NameId}] StartStepOrdersAsync: Strategy not Ready/Running. State: {State}");
                return;
            }
            if (IsAnySideAwaitingResult)
            {
                //_log.Verbose($"[{NameId}] StartStepOrdersAsync: Cannot start step orders while awaiting results from previous orders. LongAwaiting:{LongSide.IsAwaitingResult}, ShortAwaiting:{ShortSide.IsAwaitingResult}");
                return;
            }
            // ***************
            // Important NOTE: This condition doesn't include the case when Side.BaseOrder == null ... can it occur?! Wouldn't it be 'safer' to include still ?!
            // ***************
            if (!LongSide.IsBaseOrderActive && !LongSide.IsBaseOrderFilled && !ShortSide.IsBaseOrderActive && !ShortSide.IsBaseOrderFilled)
            {
                _log.Information($"[{NameId}] StartStepOrdersAsync: Attempting to place initial/retry base orders for IntendedPrice: {IntendedPrice}.");
                LongSide.RequestBaseOrderPlacement();
                ShortSide.RequestBaseOrderPlacement();
            }
            else
            {
                _log.Information($"[{NameId}] StartStepOrdersAsync: Base orders appear active or filled. LongActive:{LongSide.IsBaseOrderActive}, LongFilled:{LongSide.IsBaseOrderFilled}, ShortActive:{ShortSide.IsBaseOrderActive}, ShortFilled:{ShortSide.IsBaseOrderFilled}");
            }
        }

        private void SubscribeToExchangeEvents()
        {
            MarketDataService.SubscribeToFuturesMarketData(ExchangeAPI.TradingPair, OnFuturesMarketDataUpdate);
            ExchangeAPI.OnPositionUpdate += OnPositionUpdate;
            ExchangeAPI.OnOrderUpdate += OnOrderUpdate;
        }

        private void UnsubscribeFromExchangeEvents()
        {
            if (_exchangeAPI != null)
            {
                _exchangeAPI.OnPositionUpdate -= OnPositionUpdate;
                _exchangeAPI.OnOrderUpdate -= OnOrderUpdate;
            }
        }

        private void OnFuturesMarketDataUpdate(FuturesMarketData data)
        {
            if (State != StrategyState.Running || data == null)
                return;
            _latestFuturesData = data;
            OnMarketDataUpdate?.Invoke(data);
            if (IsAnySideAwaitingResult)
                return;
            if (_processingTask.IsCompleted)
            {
                _processingTask = Task.Run(() => ProcessStrategyLevelMarketUpdateAsync(data));
            }
            else
            {
                _log.Verbose($"[{NameId}] OnFuturesMarketDataUpdate: Processing task is still running. Skipping this update.");
            }
        }

        private async Task ProcessStrategyLevelMarketUpdateAsync(FuturesMarketData data)
        {
            if (data == null)
            {
                _log.Warning($"[{NameId}] ProcessStrategyLevelMarketUpdateAsync: Invalid market data received. Skipping processing.");
                return;
            }

            ValidateStrategyState();

            if (_strategyConfig.StopOnLiquidationPriceRatio > 0 && (_currentLongPosition != null || _currentShortPosition != null))
            {
                var positionToCheck = _currentLongPosition ?? _currentShortPosition;
                if (positionToCheck != null && positionToCheck.LiquidationPrice.HasValue && data.MarkPrice.HasValue && positionToCheck.LiquidationPrice > 0 && positionToCheck.AveragePrice.HasValue)
                {
                    decimal distanceToLiq = Math.Abs(data.MarkPrice.Value - positionToCheck.LiquidationPrice.Value);
                    decimal entryToLiq = Math.Abs(positionToCheck.AveragePrice.Value - positionToCheck.LiquidationPrice.Value);
                    if (entryToLiq > 0)
                    {
                        decimal ratio = distanceToLiq / entryToLiq;
                        if (ratio < (1 - _strategyConfig.StopOnLiquidationPriceRatio))
                        {
                            _log.Warning($"[{NameId}] STOP TRIGGERED: Liq ratio for {positionToCheck.Direction} is {ratio:P2} < threshold {1 - _strategyConfig.StopOnLiquidationPriceRatio:P2}. MarkPx: {data.MarkPrice}, LiqPx: {positionToCheck.LiquidationPrice}. Stopping & requesting removal.");
                            await StopAsync();
                            //OnRemovalRequest?.Invoke(this); // it is stopped, but sits on MainsTrategy's _strategyPool preventing to be re-used
                            return;
                        }
                    }
                }
            }
            await Task.CompletedTask;
        }

        private void ValidateStrategyState()
        {
            // Validate the strategy state based on the latest market data
            // 1. If BaseOrders are not placed, that qualify for constant retry until they are placed (or possibly put a cap of failed attempts, or a throttle)
            StartStepOrders();
            // 2. If BaseOrders are placed, that qualify for constant check if the appropriate TakeProfit orders are placed or not, and constantly retry until they are placed (or possibly put a cap of failed attempts, or a throttle)
            // For any (or both) positions are open (Long or Short with Quantity != 0), check if the appropriate pair of TakeProfit orders are placed, and if not, constantly retry to place them (or possibly put a cap of failed attempts, or a throttle)
            // But first, some sanity check between positions and base orders:
            bool isLongBaseOrderFilled = LongSide.IsBaseOrderFilled;
            bool isShortBaseOrderFilled = ShortSide.IsBaseOrderFilled;
            if ((_currentLongPosition != null) && (_currentLongPosition.Quantity != 0) && (!LongSide.IsBaseOrderFilled))
            {
                _log.Warning($"[{NameId}] Not Suppose To Happen: Long Position is Open, but LongSide BaseOrder is NOT Filled");
                isLongBaseOrderFilled = true;
            }
            if ((_currentShortPosition != null) && (_currentShortPosition.Quantity != 0) && (!ShortSide.IsBaseOrderFilled))
            {
                _log.Warning($"[{NameId}] Not Suppose To Happen: Short Position is Open, but ShortSide BaseOrder is NOT Filled");
                isShortBaseOrderFilled = true;
            }
            if (isLongBaseOrderFilled || isShortBaseOrderFilled)
            {
                if (isLongBaseOrderFilled && (!LongSide.IsTakeProfitOrderActive || LongSide.TakeProfitOrder == null))
                {
                    _log.Information($"[{NameId}] Long base order filled, attempting to place TP.");
                    LongSide.RequestTakeProfitOrderPlacement();
                }
                if (isShortBaseOrderFilled && (!ShortSide.IsTakeProfitOrderActive || ShortSide.TakeProfitOrder == null))
                {
                    _log.Information($"[{NameId}] Short base order filled, attempting to place TP.");
                    ShortSide.RequestTakeProfitOrderPlacement();
                }
            }
            // "Constantly retry" means that we will check the above states on every new market data update, and try to place the orders again.
            // (Note: if a previous attempt is still "ongoing", IsAwaitingResult/IsAnySideAwaitingResult should properly guard against placing new orders - OnFuturesMarketDataUpdate already has this properly)
            // Note2: Having this, replaces the need of initiating the orer placements in StartAsync() or becomes optional
        }

        private void OnPositionUpdate(PositionModelUpdate update)
        {
            if (State != StrategyState.Running || update == null || update.Symbol != ExchangeAPI.TradingPair.Symbol) return;
            _log.Information($"[{NameId}] Position Update: {update.Symbol} {update.Direction?.ToString() ?? update.Side?.ToString()} Qty:{update.Quantity} AvgPx:{update.AveragePrice} Status:{update.PositionStatus}");
            lock (_positionLock)
            {
                bool isLongPosition = (update.Direction == PositionDirection.Buy) ||
                                    (update.Direction == PositionDirection.OneWay && update.Side == Models.PositionSide.Buy) ||
                                    (update.Side == Models.PositionSide.Buy && update.Direction == null);

                bool isShortPosition = (update.Direction == PositionDirection.Sell) ||
                                     (update.Direction == PositionDirection.OneWay && update.Side == Models.PositionSide.Sell) ||
                                     (update.Side == Models.PositionSide.Sell && update.Direction == null);

                if (isLongPosition)
                    _currentLongPosition = (update.Quantity != 0) ? update : null;
                else if (isShortPosition)
                    _currentShortPosition = (update.Quantity != 0) ? update : null;
            }
        }

        private void OnOrderUpdate(OrderModelUpdate update)
        {
            if (State != StrategyState.Running || update == null || update.Symbol != ExchangeAPI.TradingPair.Symbol) return;
            _log.Information($"[{NameId}] Order Update: {update.ClientOrderId ?? update.OrderId} Status:{update.Status} FilledQty:{update.QuantityFilled}");

            if (LongSide != null && LongSide.IsManagingOrder(update.ClientOrderId, update.OrderId))
            {
                _log.Debug($"[{NameId}] Forwarding order update to LongSide.");
                LongSide.HandleOrderUpdate(update);
            }
            else if (ShortSide != null && ShortSide.IsManagingOrder(update.ClientOrderId, update.OrderId))
            {
                _log.Debug($"[{NameId}] Forwarding order update to ShortSide.");
                ShortSide.HandleOrderUpdate(update);
            }
            else
            {
                _log.Debug($"[{NameId}] Order update for {update.ClientOrderId ?? update.OrderId} does not belong to LongSide or ShortSide of this strategy instance.");
            }
        }

        private OrderModelUpdate CreateOrderModelUpdateFromResult(OrderResult result, FuturesOrderRequest request)
        {
            if (result == null)
            {
                _log.Error($"CreateOrderModelUpdateFromResult called with null result for ClientID {request.ClientId}");
                return CreateFailedOrderModelUpdate(request, "Internal error: OrderResult was null");
            }

            var update = new OrderModelUpdate
            {
                OrderId = result.OrderId,
                ClientOrderId = request.ClientId,
                Symbol = ExchangeAPI.TradingPair.Symbol,
                Status = result.Status,
                Side = request.IsBuy ? OrderSide.Buy : OrderSide.Sell,
                OrderType = request.OrderType,
                Quantity = request.Amount,
                Price = request.Price,
                QuantityFilled = result.ExecutedQuantity,
                AveragePrice = result.ExecutedPrice,
                IsReduceOnly = request.IsReduceOnly,
                PositionDirection = request.PositionDirection,
                Timestamp = result.Timestamp,
                RejectReason = result.IsSuccess ? null : result.Message
            };
            return update;
        }

        private OrderModelUpdate CreateFailedOrderModelUpdate(FuturesOrderRequest request, string message)
        {
            return new OrderModelUpdate
            {
                OrderId = string.Empty,
                ClientOrderId = request.ClientId,
                Symbol = ExchangeAPI.TradingPair.Symbol,
                Status = OrderStatus.Rejected,
                Side = request.IsBuy ? OrderSide.Buy : OrderSide.Sell,
                OrderType = request.OrderType,
                Quantity = request.Amount,
                Price = request.Price,
                IsReduceOnly = request.IsReduceOnly,
                PositionDirection = request.PositionDirection,
                Timestamp = DateTime.UtcNow,
                RejectReason = message,
                QuantityFilled = 0,
                AveragePrice = null,
                ExecutedFee = null
            };
        }

        private async Task<bool> ValidateExchangeState()
        {
            _log.Information("[VALIDATE] Starting exchange state validation:");

            if (ExchangeAPI.State != ExchangeState.Ready)
            {
                _log.Error($"[VALIDATE] ExchangeAPI not Ready. Current state: {ExchangeAPI.State}");
                return false;
            }
            try
            {
                _log.Information("[VALIDATE] Checking position mode");
                var positionMode = await ExchangeAPI.GetPositionModeAsync();
                _log.Information($"[VALIDATE] Current position mode: {positionMode}");

                if (positionMode != PositionMode.BothSides)
                {
                    _log.Error($"[VALIDATE] Exchange is not in Hedge Mode (BothSides). Current mode: {positionMode}. Please configure the exchange account.");
                    return false;
                }

                _log.Information("[VALIDATE] Position mode is correctly set to BothSides (Hedge Mode)");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "[VALIDATE] Failed to validate exchange settings (Position Mode/Leverage).");
                return false;
            }

            _log.Information("[VALIDATE] Exchange state validation completed successfully");
            return true;
        }

        public bool IsValidApiForReconstruction(FetchedPositionsResult positions, IEnumerable<OrderModel> orders)
        {
            var orderList = orders?.ToList() ?? new List<OrderModel>();
            var longPosOpen = positions.LongPosition != null && positions.LongPosition.Quantity != 0;
            var shortPosOpen = positions.ShortPosition != null && positions.ShortPosition.Quantity != 0;

            // Case 1: Neither long nor short position is open
            if (!longPosOpen && !shortPosOpen)
            {
                // Must have exactly 2 orders: one long (Buy, !ReduceOnly), one short (Sell, !ReduceOnly)
                if (orderList.Count != 2)
                    return false;

                bool hasLongOrder = orderList.Any(o => o.Side == OrderSide.Buy && !o.IsReduceOnly);
                bool hasShortOrder = orderList.Any(o => o.Side == OrderSide.Sell && !o.IsReduceOnly);

                return hasLongOrder && hasShortOrder;
            }

            // Case 2: At least one position is open, must have a corresponding opposite-sided order for each open position
            if (longPosOpen)
            {
                // There must be a short order (Sell, ReduceOnly) for the long position
                bool hasShortReduceOrder = orderList.Any(o =>
                    o.Side == OrderSide.Sell &&
                    o.IsReduceOnly &&
                    (o.PositionDirection == PositionDirection.Buy || o.PositionDirection == PositionDirection.OneWay)
                );
                if (!hasShortReduceOrder)
                    return false;
            }

            if (shortPosOpen)
            {
                // There must be a long order (Buy, ReduceOnly) for the short position
                bool hasLongReduceOrder = orderList.Any(o =>
                    o.Side == OrderSide.Buy &&
                    o.IsReduceOnly &&
                    (o.PositionDirection == PositionDirection.Sell || o.PositionDirection == PositionDirection.OneWay)
                );
                if (!hasLongReduceOrder)
                    return false;
            }

            // If both positions are open, both opposite-sided orders must exist (checked above)
            // If only one position is open, only its opposite order must exist

            return true;
        }

        // TODO: Regading to this Snapshot, this should be an 'another' snapshot for a new UI window/view where the data specific to HedgeGridStrategy is shown
        public StrategyUIData? GetUIDataSnapshot()
        {
            var snapshot = new StrategyUIData
            {
                StrategyStatus = this.State.ToString(),
                APIStatus = this.ExchangeAPI?.State ?? ExchangeState.Stopped,
                Timestamp = DateTime.UtcNow
            };

            var latestMarketData = _marketDataService?.GetLatestData();
            var latestSpot = latestMarketData?.Spot;

            snapshot.SpotLastPrice = latestSpot?.LastPrice;
            snapshot.SpotBid = latestSpot?.HighestBid;
            snapshot.SpotAsk = latestSpot?.LowestAsk;

            snapshot.FuturesLastPrice = _latestFuturesData?.LastPrice;
            snapshot.FuturesMarkPrice = _latestFuturesData?.MarkPrice;
            snapshot.FuturesBid = _latestFuturesData?.HighestBid;
            snapshot.FuturesAsk = _latestFuturesData?.LowestAsk;
            snapshot.FuturesFundingRate = _latestFuturesData?.FundingRate;

            PositionModel? longPosSnapshot = null;
            PositionModel? shortPosSnapshot = null;
            lock (_positionLock)
            {
                longPosSnapshot = _currentLongPosition;
                shortPosSnapshot = _currentShortPosition;
            }

            snapshot.LongPositionSize = longPosSnapshot?.Quantity;
            snapshot.LongPositionEntry = longPosSnapshot?.AveragePrice;
            snapshot.ShortPositionSize = shortPosSnapshot?.Quantity;
            snapshot.ShortPositionEntry = shortPosSnapshot?.AveragePrice;

            bool isActiveStep = (this.LongSide != null && this.ShortSide != null &&
                                 (this.LongSide.IsBaseOrderActive || this.LongSide.IsBaseOrderFilled ||
                                  this.ShortSide.IsBaseOrderActive || this.ShortSide.IsBaseOrderFilled));
            snapshot.OpenStepCount = isActiveStep ? 1 : 0;
            snapshot.HighestStepPrice = isActiveStep ? this.IntendedPrice : (decimal?)null;
            snapshot.LowestStepPrice = isActiveStep ? this.IntendedPrice : (decimal?)null;

            if (isActiveStep)
            {
                Func<OrderPair?, string> getStatus = (pair) => {
                    if (pair == null) return "ERR";
                    if (pair.IsBaseOrderFilled)
                    {
                        if (pair.TakeProfitOrder == null) return "TP_P";
                        if (pair.IsTakeProfitOrderActive) return "TP_A";
                        if (pair.IsTakeProfitOrderFilled) return "TP_F_CYCLE";
                        return "F_TP_UNK";
                    }
                    return pair.IsBaseOrderActive ? "B_A" : "B_P";
                };
                snapshot.LastFiveStepsInfo = new List<string> {
                    $"Step @ {this.IntendedPrice:F2} L:{getStatus(this.LongSide)} S:{getStatus(this.ShortSide)}"
                };
            }
            else
            {
                snapshot.LastFiveStepsInfo = new List<string>();
            }

            var lastWalletUpdate = this.ExchangeAPI?.GetLastWalletUpdate();
            snapshot.TotalEquity = lastWalletUpdate?.TotalEquity;

            decimal? currentStepUnrealizedPnl = 0m;
            if (longPosSnapshot?.UnrealizedPnl != null) currentStepUnrealizedPnl += longPosSnapshot.UnrealizedPnl;
            if (shortPosSnapshot?.UnrealizedPnl != null) currentStepUnrealizedPnl += shortPosSnapshot.UnrealizedPnl;
            snapshot.UnrealizedPnL = (currentStepUnrealizedPnl == 0m && longPosSnapshot == null && shortPosSnapshot == null) ? (decimal?)null : currentStepUnrealizedPnl;

            snapshot.RealizedPnL = this.ExchangeAPI?.GetSessionRealizedPnl();

            snapshot.TotalWalletBalance = lastWalletUpdate?.TotalWalletBalance;
            snapshot.TotalAvailableBalance = lastWalletUpdate?.TotalAvailableBalance;

            if (lastWalletUpdate?.Assets != null)
            {
                var usdtAsset = lastWalletUpdate.Assets.FirstOrDefault(a => a.Asset.Equals("USDT", StringComparison.OrdinalIgnoreCase));
                if (usdtAsset != null)
                {
                    snapshot.USDTWalletBalance = usdtAsset.Total;
                    snapshot.USDTAvailableBalance = usdtAsset.Available;
                }
            }

            snapshot.StrategyCalculatedRealizedPnL = this.AccumulatedCalculatedRealizedPnL;
            snapshot.PeakStrategyRealizedPnL = this.PeakStrategyCalculatedRealizedPnL;
            snapshot.TroughStrategyRealizedPnL = (this.TroughStrategyCalculatedRealizedPnL == decimal.MaxValue) ? (decimal?)null : this.TroughStrategyCalculatedRealizedPnL;
            snapshot.TotalStrategyFees = this.AccumulatedReportedFees.Quote.Amount;

            snapshot.LastErrorMessages = _recentErrors.ToList();

            return snapshot;
        }

        public async Task ConsolidateAsync()
        {
            bool isSucceded = false;
            try
            {
                await Task.WhenAll(CancelAllOrdersAsync(), CloseAllPostionsAsync()).WaitAsync(TimeSpan.FromSeconds(10));
                isSucceded = true;
            }
            catch (TimeoutException)
            {
                _log.Error("Consolidation failed: Timeout or exception occurred after 10 seconds.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Consolidation failed: An unexpected error occurred.");
            }
            finally
            {
                if (isSucceded)
                    _log.Information("Consolidation completed successfully.");
                else
                    _log.Error("Consolidation failed.");
            }
        }

        public async Task CancelAllOrdersAsync()
        {
            try
            {
                await _exchangeAPI.CancelAllOrdersAsync();
            }
            catch
            {
                throw;
            }
        }

        public async Task CloseAllPostionsAsync()
        {
            try
            {
                await _exchangeAPI.CloseAllPositionsAsync();
            }
            catch
            {
                throw;
            }
        }
    }
}

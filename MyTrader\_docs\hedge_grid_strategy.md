# Hedge Grid Strategy Description

This document outlines the concept and logic for the Hedge Grid trading strategy.

## Overview

The strategy aims to profit from price volatility in perpetual futures markets (specifically BTC/USDT initially). It operates under the assumption that the exchange account is configured for **Hedge Mode (BothSides)**, allowing simultaneous long and short positions for the same contract.

1.  **Step-Based Entries:** (a.k.a. parameters of HedgeGridStrategyConfig)
    *   Define a price interval (`StepSize`, e.g., 100 USDT).
    *   Define a position size (`PositionSizePerStep`, e.g., 0.01 BTC).
    *   Define a target profit distance (`ProfitTargetDistance`, e.g. 200m USDT)
    *   Define the leverage (e.g., 100x).
    *   Symbol is NOT defined here, that is passed from ExchangeTrader in a proper elegant way down to the strategies. (eg. BaseExchangeAPI is already created with the corresponding trading symbol, and from there it can be used everywhere in desendant cases!)
    *   Define BaseOrderTimeInForce - FillOrKill
    *   Define TakeProfitOrderTimeInForce - GoodTillCancel
    *   Define BaseOrderType - Limit
    *   Define TakeProfitOrderType - Limit
    *   Define BaseOrderLimitPriceAdjustment - in USDT - the 'added' to the limit price to increase the chance of getting filled by having a bet deeper in the orderbook
    *   Define MaxActiveTrades - default value 0 - we can have this, but usually we will use the full amount of pool elements, this just for if we want not all elements to be used for some reason (0 value means 'use all the pool elements') -- *IMPORTANT* probably this has to be moved into MainStrategyConfig !
    *   Define StopOnLiquidationPriceRatio - default 0.8m - When we are 80% toward the liquidation price, stop that sub-strategy HedgeGridStrategy (stop, or consolidate/cancel all orders and positons. release back to the pool, etc. will see, or just do nothing - will decide)
    *   Define MaxInitialStepSpreadTolerancePercentage = 0.0002m; // 0.02% default - 0.02% above or below of IntendedPrice we will do trade decisions (reopening, etc.)
    *   Every other not mentioned here parameters to be discarded!

The config of ExchangeTrader and the config of MainStrategy has to be discussed/decided and stated in _list_of_problems.md

    ## Core Logic

**Description of trading flow**

  1. The first step is a somewhat special case.
  When the application starts, there is an initial futures ticker bid and ask price. This comes from the _marketDataService MainStrategy was constructed with.
  The first point/step is placed "as fast as possible" meaning here we put a hedged short and long order at the same time one with the bid price, one with the ask price (+ BaseOrderLimitPriceAdjustment)
  We can try this with PostOnly limit orders, but after 3 - 5 failed attempts, - *in practice, for now, we just place straight market orders*.
  When the 2 orders are filled, we average the entry prices, and that will be the IntendedPrice for the first step.
  As soon as the 2 inital market order is filled and we have the IntendedPrice, that triggers the placement of the two TakeProfitOrders.
  One for the long position, one for the short position, but for these TakeProfitOrders we can try to use (enforce) PostOnly and ReduceOnly orders.
  (to increase our chances we could generate some random offseted IntendedPrice + ProfitTargetDistance + random(interval in MaxInitialStepSpreadTolerancePercentage) USDT distance, with each ticker recalculated this value, so I expect this to be successful, but will see!)
  When these 2 TakeProfitOrders are successfully placed, that triggers the creation of 2 more HedgridStrategy sub-strategy instances:
   - One is initialized with Intended price of currentIntendedPrice + StepSize, and the other one is with currentIntendedPrice - StepSize. 
   - This means they attempt to set their PostOnly BaseOrders (Long and Short) (of course using the BaseOrderLimitPriceAdjustment +/-)
  And when the BaseOrders are placed successfully, that's it, those 2 new sub-strategies do nothing else for now.
  2. Next we wait for the price to move `StepSize` away from the IntendedPrice. This will cause to trigger *one of the* just previously placed 2 new HedgeGridStrategy sub-strategies
  BaseOrders to be filled. We wait for both of them to successfully fill.
  When that happened, that triggerrs the complete cancellations of the other HedgeGridStrategy sub-strategies (which is now obvioulsy now 2x at distance from the current price) and
  also in the meantime the appropriate PostOnly TakeProfitOrders are attempted to be placed.
  3. So far we are 100 USDT distance (StepSize) from the very first IntendedPrice, and let's say the price continues to move in the same direction, 
  reaching 200 USDT disntance (`ProfitTargetDistance`) from the very first IntendedPrice. At this moment the appropriate PostOnly AND ReduceOnly !! TakeProfit order fills.
  This immediately triggers the re-placement of the 'original' BaseOrder paire to this TakeProfit order just filled, in PostOnly limit way (but NOT reduceOnly, because Only TakeProfitOrders are ReduceOnly)
  Also since we are again 100 USDT distance from the previous 100 USDT distance, this qualifies as condition to trigger the fullfillment of one of the new HedgeGridStrategy sub-strategies 
  created right after the base fillment of the very first initial strategy, and that triggers the creation of 2 new upper and lower HedgeGridStrategy sub-strategies, repeating the above cycle ...
  4. At this point can happen 2 things: the price continously moves in the same direction, or it reverses. If it continues, the above is repeated, and if it reverses, 
  the above described things can happen, plus if the BaseOrder was re-placed, it might be filled again, triggering the re-placemnt of it's TakeProfit order, and so on.
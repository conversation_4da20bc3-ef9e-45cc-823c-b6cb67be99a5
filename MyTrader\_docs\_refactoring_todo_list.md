﻿# Refactoring ToDo List: Hedge Grid Strategy Implementation

This document outlines the tasks required to refactor the codebase to implement the new Hedge Grid trading strategy as per `new_architecture.md` and `hedge_grid_strategy.md`.

**Status Legend:**
*   `[ ]` - Not Started
*   `[/]` - In Progress / Partially Implemented
*   `[x]` - Done / Implemented

## I. Core Architecture & Strategy Refactoring (`MainStrategy` & `HedgeGridStrategy`)

1.  **`MainStrategy` Refinement:**
    *   `[ ]` **Problem:** `MainStrategy.cs` requires full implementation for managing API pools, strategy pools, and orchestrating `HedgeGridStrategy` instances.
    *   **ToDo:**
        *   `[ ]` Implement robust management of `_apiPool` (APIs received from `ExchangeTrader`, identified by `BaseExchangeAPI.Name`) and `_strategyPool` (active `HedgeGridStrategy` instances). This includes acquiring an API for a new strategy and releasing it when a strategy is removed.
        *   `[ ]` **`MainStrategy.InitializeAsync()` Logic:**
            *   `[ ]` Receive the list of `BaseExchangeAPI` instances from `ExchangeTrader`.
            *   `[ ]` Load `MainStrategyConfig.json` (see item 4) to identify which named APIs from the `ExchangeTrader` pool are designated for use.
            *   `[ ]` **State Reconstruction/Initial `_strategyPool` Population:**
                *   Iterate through each designated `BaseExchangeAPI`.
                *   For each API, instantiate a `HedgeGridStrategy` and call its `InitializeAsync()`.
                *   If `HedgeGridStrategy.InitializeAsync()` is successful (i.e., it reconstructed an existing state or is ready for a new step if `MainStrategy` later decides to use this API for a blank slate start), add the strategy to `_strategyPool`.
                *   If, after checking all designated APIs, `_strategyPool` is empty (no reconstructible steps found), then `MainStrategy` will use one of the available "empty" APIs to initiate the "First Step Special Case" (see item 2.1).
            *   `[ ]` **Market Data Subscription:**
                *   During initial population, if `_strategyPool` becomes non-empty, temporarily subscribe to the `OnMarketDataUpdate` of the first successfully initialized `HedgeGridStrategy`.
                *   After all designated APIs are processed and `_strategyPool` is finalized (or the first step is created), determine the "active frontier" `HedgeGridStrategy` (e.g., closest `IntendedPrice` to current market).
                *   Re-subscribe `MainStrategy.OnFuturesMarketDataUpdate` to this "active frontier" `HedgeGridStrategy`'s `OnMarketDataUpdate` event. This subscription must be updated as the active frontier of the grid changes.
        *   `[ ]` **`CreateNewHedgeGridStepAsync()` Method (renamed from `InitiateNewTradePointAsync`):**
            *   Used by `MainStrategy` to expand the grid *after* initial setup/reconstruction.
            *   Acquire an available `BaseExchangeAPI` from `_apiPool`.
            *   Instantiate a new `HedgeGridStrategy` with the API, its associated `IMarketDataService`, `HedgeGridStrategyConfig`, and the calculated `intendedPrice`.
            *   Call `InitializeAsync()` on the new strategy (which for a new step on an "empty" API will set it up for initial order placement).
            *   Add the successfully initialized strategy to `_strategyPool`.
        *   `[ ]` Implement `OnStrategyRemovalRequest` handler to release the `BaseExchangeAPI` of a removed/completed `HedgeGridStrategy` back to `_apiPool`.

2.  **`HedgeGridStrategy` Full Implementation:**
    *   `[ ]` **Problem:** `HedgeGridStrategy.cs` needs a complete overhaul, removing legacy `OrderPoint`/`TradePoint` and adapting `OrderPair.cs`.
    *   **ToDo:**
        *   `[ ]` Remove all dependencies on `OrderPoint.cs` and `TradePoint.cs`.
        *   `[ ]` Refactor/Adapt `OrderPair.cs`: Each `HedgeGridStrategy` will have two `OrderPair` instances (`LongSide`, `ShortSide`) to manage the base order and TP order for one side of its hedged step.
        *   `[ ]` **Implement Core Trading Logic (as per `hedge_grid_strategy.md`):**
            *   `[ ]` **Self-Managed Step Initialization (called by `InitializeAsync` or a dedicated method):**
                *   If starting a new step (not reconstructing): Place hedged short/long base orders via its `OrderPair`s (PostOnly limit, then market if fails).
                *   On base order fills: Calculate its own `IntendedPrice`. - but only on "First Step Special Case" otherwise stick to the calculated value.
                *   Place its TakeProfitOrders via `OrderPair`s (PostOnly, ReduceOnly).
                *   Signal readiness/activation to `MainStrategy`.
            *   `[ ]` **`MainStrategy`-Triggered New Step Creation:** `MainStrategy` will instantiate new `HedgeGridStrategy` instances when the grid needs to expand (e.g., initial blank slate, or price moves `StepSize`).
            *   `[ ]` **`MainStrategy`-Triggered Cancellation:** `MainStrategy` will identify and request cancellation of distant `HedgeGridStrategy` instances.
            *   `[ ]` **Take-Profit Fill Logic (within `OrderPair`):**
                *   On TP fill, the `OrderPair` immediately re-places its BaseOrder (PostOnly limit, NOT ReduceOnly).
                *   The parent `HedgeGridStrategy` is notified.
        *   `[ ]` Ensure `HedgeGridStrategy` uses parameters from `HedgeGridStrategyConfig.cs` (excluding `Symbol` and `maxActiveStrategies`).
        *   `[ ]` Correctly use `OrderType`, `TimeInForce`, and `reduceOnly` flags, with PostOnly as the primary attempt, and others as fallbacks.
        *   `[ ]` Implement `BaseOrderLimitPriceAdjustment` in `OrderPair` when placing base orders.
        *   `[ ]` Implement `StopOnLiquidationPriceRatio` logic within `HedgeGridStrategy` (action: TBD - cancel & release, or do nothing).
        *   `[ ]` Implement `MaxInitialStepSpreadTolerancePercentage` for re-opening/trade decisions.
        *   `[ ]` Ensure the `OnMarketDataUpdate` event is correctly invoked with data from its `IMarketDataService`.

3.  **`HedgeGridStrategy` State Reconstruction:**
    *   `[ ]` **Problem:** Re-establish `HedgeGridStrategy` state on startup from exchange data.
    *   **ToDo:**
        *   **`HedgeGridStrategy.InitializeAsync()`:**
            *   Fetch its positions and orders using its assigned `_exchangeAPI`.
            *   Reconstruct its `LongSide` and `ShortSide` `OrderPair` states:
                *   If non-zero positions exist for its step: Base orders are filled. Relevant pending orders are TPs.
                *   If zero positions: Relevant pending orders are initial BaseOrders.
            *   If no relevant positions/orders for its expected step, `InitializeAsync` should indicate this (e.g., return `false` or throw a specific exception) so `MainStrategy` doesn't add it to the active pool unless it's intended for a new blank step.
            *   Set its `IntendedPrice` based on reconstructed data or as provided by `MainStrategy` for new steps.
        *   `[ ]` **Update `hedge_grid_strategy.md`:** Reflect this reconstruction approach. The "first step is a somewhat special case" applies *only if* `MainStrategy` determines a complete blank slate across all its designated APIs during its own initialization. Otherwise, the grid is reconstructed from existing exchange state.

4.  **Configuration Files Definition:**
    *   `[/]` **Problem:** Finalize `MainStrategyConfig.json` and `HedgeGridStrategyConfig.cs`.
    *   **ToDo:**
        *   `[x]` Define `MainStrategyConfig.cs` (C# class done).
        *   `[ ]` Define structure for `MainStrategyConfig` in `strategies.json` or a dedicated file (e.g., `main_strategy_config.json`):
            *   `apiNamesToUse`: (string array) List of `BaseExchangeAPI.Name` strings.
            *   `maxActiveStrategies`: (int) Max concurrent `HedgeGridStrategy` instances (0 for all designated APIs).
            *   Any other `MainStrategy`-level parameters.
        *   `[x]` Finalize `HedgeGridStrategyConfig.cs` (C# class done): Ensure all parameters from `hedge_grid_strategy.md` (StepSize, etc.) are present, EXCLUDING `Symbol` and `MaxActiveTrades`.
        *   `[x]` Update `ConfigurationLoader.cs` to create default `strategies.json` with entries for `MainStrategyConfig` and `HedgeGridStrategyConfig`.
        *   `[ ]` Update `ConfigurationLoader.cs` to correctly load user-customized values for these from `strategies.json` (or the new dedicated file if chosen).

## II. API Layer (`BaseExchangeAPI` & Concrete Implementations)

5.  **Leverage Handling:**
    *   `[x]` **Clarification:** Same leverage for Long/Short. `ExchangeConfig.Leverage` is the source.
    *   `[x]` **ToDo:** Confirm `BybitExchangeAPI.SetLeverageAsync` applies this correctly. (It was tested in the previous iterations, considered as working).

6.  **Order Placement Validation (`ValidatePositionDirectionAndMode`):**
    *   `[x]` **Clarification:** Likely okay as directions are provided concretely.
    *   `[x]` **ToDo:** Mark for confirmation during testing. - Pretty sure works, marked as done

7.  **`PositionMode` Synchronization (Bybit):**
    *   `[ ]` **Clarification:** Bybit V5 API may not allow fetching `AccountSettings`. Strategy relies on config and API's attempt to set `PositionMode.BothSides`.
    *   `[ ]` **ToDo:**
        *   Retain `BybitExchangeAPI.InitializeAsync` logic to *attempt* to set `PositionMode` from `_config`.
        *   Add code comments re: Bybit V5 limitation.

8.  **Order Final State Handling (`WaitForOrderStatusAsync`):**
    *   `[x]` **Clarification:** Worked previously.
    *   `[ ]` **ToDo:** Keep for review/confirmation during integration testing.

## III. General Code Quality & Refinements

9.  **Logging:**
    *   `[ ]` **Clarification:** Sensible, not overly verbose.
    *   `[ ]` **ToDo:** Review and implement appropriate logging.

10. **Asynchronous Operations:**
    *   `[ ]` **Clarification:** Adhere to no fire-and-forget.
    *   `[ ]` **ToDo:** Double-check async code.

11. **Error Handling & Resilience:**
    *   `[ ]` **Clarification:** `IsAnySideAwaitingResult` (or similar in `OrderPair`) is a key safeguard.
    *   `[ ]` **ToDo:** Implement try-catch for API calls. `IsAnySideAwaitingResult` in `HedgeGridStrategy`/`OrderPair`s to prevent conflicting ops.

12. **`MainStrategy` Configuration:**
    *   `[ ]` **Clarification:** Uses `BaseExchangeAPI.Name`. `maxActiveStrategies` in `MainStrategyConfig`.
    *   `[ ]` **ToDo:** (Covered by item 4).

13. **Legacy Code Cleanup:**
    *   `[/]` **Problem:** `OrderPoint.cs` and `TradePoint.cs` are legacy.
    *   **Clarification (User Note 13):** `OrderPoint.cs` and `TradePoint.cs` will be removed. `OrderPair.cs` will be kept and adapted for the new `HedgeGridStrategy` to manage one side of a step's hedge (base order + TP order).
    *   **ToDo:** 
        *   `[x]` Refactor and integrate `OrderPair.cs` into `HedgeGridStrategy.cs` (OrderPair.cs methods implemented).
        *   `[ ]` Systematically remove `OrderPoint.cs` and `TradePoint.cs` and update all references (after HedgeGridStrategy uses the new OrderPair).

14. **`HedgeGridStrategyConfig.Symbol`:**
    *   `[x]` **Problem:** Discrepancy in documentation regarding `Symbol` in `HedgeGridStrategyConfig`.
    *   **Clarification (User Note 14):** `Symbol` comes from `BaseExchangeAPI`, which is configured via `AppConfig`.
    *   **ToDo:** Remove `Symbol` from `HedgeGridStrategyConfig.cs` (Done as part of item 4).

15. **`HedgeGridStrategyConfig.MaxActiveTrades` vs. `MainStrategyConfig.maxActiveStrategies`:**
    *   `[x]` **Problem:** Location of this configuration.
    *   **Clarification (User Note 15 & 1):** This parameter (`maxActiveStrategies`) belongs in `MainStrategyConfig`.
    *   **ToDo:** Ensure `MaxActiveTrades` is removed from `HedgeGridStrategyConfig.cs` and `maxActiveStrategies` is added to the definition of `MainStrategyConfig` (Done as part of item 4).

16. **UI Data Provision (`
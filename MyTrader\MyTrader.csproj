﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="System.Reactive" Version="6.0.1" />
    <PackageReference Include="Terminal.Gui" Version="1.18.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Bybit.Net\ByBit.Net\Bybit.Net.csproj" />
    <ProjectReference Include="..\ExchangeSharp-Core\ExchangeSharp.csproj" />
  </ItemGroup>

</Project>

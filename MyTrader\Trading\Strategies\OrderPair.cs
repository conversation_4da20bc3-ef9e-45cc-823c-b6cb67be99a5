﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;
using MyTraderSpace.Utils;
using MyTraderSpace.Exchanges;
using static MyTraderSpace.Exchanges.ExchangeConfig;

namespace MyTraderSpace.Trading.Strategies
{
    public class OrderPair
    {
        private readonly LogManager _log;
        private readonly HedgeGridStrategyConfig _strategyConfig;
        private readonly CurrencyPair _tradingPair;
        private readonly FeeConfig _feeRate;
        
        public bool IsLong { get; }

        private string _name = string.Empty;
        public string Name => _name + (IsLong ? "_LongSide" : "_ShortSide");

        private readonly bool _isInitialPair = false;

        // Order State - store the most recent update for each
        public OrderModelUpdate? BaseOrder { get; private set; }
        public OrderModelUpdate? TakeProfitOrder { get; private set; }

        public decimal IntendedPrice { get; set; } = 0;

        public decimal? ReportedRealizedPnL { get; private set; } // GrossPnL - Fees = NetPnL
        public decimal? CalculatedRealizedPnL { get; private set; } // GrossPnL - Fees = NetPnL
        public Fee? ReportedFees { get; private set; }
        public Fee? CalcFees { get; private set; }

        public event Action<OrderPair>? OnBaseOrderFilled;
        public event Action<OrderPair>? OnTakeProfitOrderFilled;
        public event Action<OrderPair, FuturesOrderRequest>? OnOrderPlacementRequest;
        public event Action<OrderPair, OrderResult>? OnError;

        public OrderResult? LastError { get; private set; }
        
        public bool IsAwaitingResult => BaseOrder?.Status == OrderStatus.Created || TakeProfitOrder?.Status == OrderStatus.Created;
        public bool IsBaseOrderActive => BaseOrder != null && !BaseExchangeAPI.IsOrderInFinalState(BaseOrder.Status);
        public bool IsBaseOrderFilled => BaseOrder?.Status == OrderStatus.Filled;

        public bool IsTakeProfitOrderActive => TakeProfitOrder != null && !BaseExchangeAPI.IsOrderInFinalState(TakeProfitOrder.Status);
        public bool IsTakeProfitOrderFilled => TakeProfitOrder?.Status == OrderStatus.Filled;
        
        public OrderPair(string name, bool isLong, CurrencyPair tradingPair, FeeConfig feeRate, HedgeGridStrategyConfig strategyConfig, LogManager log, bool IsInitialPair = false)
        {
            _name = name;
            IsLong = isLong;
            _isInitialPair = IsInitialPair;
            _tradingPair = tradingPair;
            _feeRate = feeRate ?? throw new ArgumentNullException(nameof(feeRate));
            _log = log ?? throw new ArgumentNullException(nameof(log));
            _strategyConfig = strategyConfig ?? throw new ArgumentNullException(nameof(strategyConfig));
            
            ReportedFees = new Fee(_tradingPair);
            CalcFees = new Fee(_tradingPair);
            _log.Information($"OrderPair '{Name}' (IsLong: {IsLong}) created for {_tradingPair.Symbol}.");
        }

        private bool IsThisOrder(string? clientId = null, string? Id = null)
        {
            // Prioritize ClientID if available and matches
            if (!string.IsNullOrEmpty(clientId) && ((BaseOrder?.ClientOrderId == clientId) || (TakeProfitOrder?.ClientOrderId == clientId)))
            {
                return true;
            }
            // Fallback to OrderID if ClientID didn't match or wasn't provided
            if (!string.IsNullOrEmpty(Id) && ((BaseOrder?.OrderId == Id) || (TakeProfitOrder?.OrderId == Id)))
            {
                return true;
            }
            return false;
        }

        public bool IsManagingOrder(string? clientOrderId, string? orderId)
        {
            if (!string.IsNullOrEmpty(clientOrderId))
            {
                if (BaseOrder?.ClientOrderId == clientOrderId && !string.IsNullOrEmpty(BaseOrder.ClientOrderId)) return true;
                if (TakeProfitOrder?.ClientOrderId == clientOrderId && !string.IsNullOrEmpty(TakeProfitOrder.ClientOrderId)) return true;
            }
            if (!string.IsNullOrEmpty(orderId))
            {
                if (BaseOrder?.OrderId == orderId && !string.IsNullOrEmpty(BaseOrder.OrderId)) return true;
                if (TakeProfitOrder?.OrderId == orderId && !string.IsNullOrEmpty(TakeProfitOrder.OrderId)) return true;
            }
            return false;
        }

        // TODO: Make sure this properly calculates the Fees and PnL for both BaseOrder filling case and TakeProfitOrder filling case.
        private void CalculatePnlAndFees()
        {
            if (!IsBaseOrderFilled || !IsTakeProfitOrderFilled ||
                BaseOrder?.AveragePrice == null || TakeProfitOrder?.AveragePrice == null ||
                BaseOrder?.QuantityFilled == null || TakeProfitOrder?.QuantityFilled == null)
            {
                _log.Warning($"OrderPair '{Name}': Cannot calculate PnL. Missing required order data. BaseFilled: {IsBaseOrderFilled}, TPFilled: {IsTakeProfitOrderFilled}");
                CalculatedRealizedPnL = null;
                ReportedRealizedPnL = null;
                CalcFees = new Fee(_tradingPair); // Reset fees to zero for the pair
                ReportedFees = new Fee(_tradingPair); // Reset fees to zero for the pair
                return;
            }

            Fee currentFees = new Fee(_tradingPair);
            if (BaseOrder.ExecutedFee != null)
            {
                currentFees += BaseOrder.ExecutedFee.Value;
            }
            if (TakeProfitOrder.ExecutedFee != null)
            {
                currentFees += TakeProfitOrder.ExecutedFee.Value;
            }

            ReportedFees = currentFees.RoundTo(_tradingPair);

            currentFees = new Fee(_tradingPair);
            if ((BaseOrder.AveragePrice != null) && (BaseOrder.QuantityFilled != null))
            {
                bool isMaker = BaseOrder.OrderType == OrderType.Limit;
                decimal feeRate = _feeRate.GetFuturesFee(isMaker);
                decimal calcFee = BaseOrder.QuantityFilled.Value * feeRate;
                currentFees += new Fee(
                    new CurrencyAmount(_tradingPair.BaseCoin, 0m),
                    new CurrencyAmount(_tradingPair.QuoteCoin, calcFee)
                );
            }
            if ((TakeProfitOrder.AveragePrice != null) && (TakeProfitOrder.QuantityFilled != null))
            {
                bool isMaker = TakeProfitOrder.OrderType == OrderType.Limit;
                decimal feeRate = _feeRate.GetFuturesFee(isMaker);
                decimal calcFee = TakeProfitOrder.QuantityFilled.Value * feeRate;
                currentFees += new Fee(
                    new CurrencyAmount(_tradingPair.BaseCoin, 0m),
                    new CurrencyAmount(_tradingPair.QuoteCoin, calcFee)
                );
            }

            CalcFees = currentFees.RoundTo(_tradingPair);

            decimal entryPrice = BaseOrder.AveragePrice!.Value;
            decimal exitPrice = TakeProfitOrder.AveragePrice!.Value;
            decimal quantity = TakeProfitOrder.QuantityFilled!.Value;

            decimal calculatedGrossPnl = 0m;
            decimal reportedGrossPnl = TakeProfitOrder?.ClosedPnl ?? 0m;
            if (quantity <= 0)
            {
                _log.Warning($"OrderPair '{Name}': PnL quantity is zero or negative ({quantity}). ManualGrossPnL set to 0.");
            }
            else
            {
                if (IsLong)
                {
                    calculatedGrossPnl = (exitPrice - entryPrice) * quantity;
                }
                else // Short
                {
                    calculatedGrossPnl = (entryPrice - exitPrice) * quantity;
                }
            }
            CalculatedRealizedPnL = calculatedGrossPnl - (CalcFees?.Quote.Amount ?? 0m);
            ReportedRealizedPnL = reportedGrossPnl - (ReportedFees?.Quote.Amount ?? 0m);

            _log.Information($"OrderPair '{Name}' PnL CALC: NetPnL={CalculatedRealizedPnL:F2}, GrossCalc={calculatedGrossPnl:F2}, Fees CALC={CalcFees?.Quote.Amount ?? 0m:F2},{Environment.NewLine}" +
                             $"                   PnL REP:  NetPnL={ReportedRealizedPnL:F2}, GrossRep={reportedGrossPnl:F2}, Fees REP={ReportedFees?.Quote.Amount ?? 0m}");

            if (ReportedRealizedPnL.HasValue && CalculatedRealizedPnL.HasValue)
            {
                // Net PnL for comparison
                decimal discrepancyThreshold = 0.01m; 
                decimal discrepancy = Math.Abs(CalculatedRealizedPnL.Value - ReportedRealizedPnL.Value);
                if (discrepancy > discrepancyThreshold)
                {
                    _log.Warning($"OrderPair '{Name}' PnL DISCREPANCY: {discrepancy:F2} CalcNet={CalculatedRealizedPnL.Value:F2} vs ReportedNet={ReportedRealizedPnL.Value:F2}");
                }
            }
        }

        public void HandleOrderUpdate(OrderModelUpdate update)
        {
            if (!IsManagingOrder(update.ClientOrderId, update.OrderId))
            {
                _log.Error($"*NOT SUPPOSE TO HAPPEN*: OrderPair '{Name}': Received update for an order (Cloid: {update.ClientOrderId}, Oid: {update.OrderId}) that it's not actively managing. Ignoring.");
                return;
            }

            bool isBaseUpdate = (BaseOrder?.ClientOrderId == update.ClientOrderId || BaseOrder?.OrderId == update.OrderId);
            bool isTpUpdate = (TakeProfitOrder?.ClientOrderId == update.ClientOrderId || TakeProfitOrder?.OrderId == update.OrderId);

            if (isBaseUpdate)
            {
                BaseOrder = update;
                if (BaseOrder.Status == OrderStatus.Filled)
                {
                    _log.Information($"OrderPair '{Name}': Base order filled. Triggering OnBaseOrderFilled event.");
                    CalculatePnlAndFees();
                    // It is done in parent HedgeGridStrategy, so in case fails, ValidateStrategyState() always retries this at each ticker
                    RequestTakeProfitOrderPlacement();
                    OnBaseOrderFilled?.Invoke(this);
                }
                else if (BaseExchangeAPI.IsOrderInFinalState(BaseOrder.Status))
                {
                    _log?.Warning($"OrderPair {Name}: Base order failed/cancelled. Status: {update.Status}, Reason: {update.RejectReason}");
                    LastError = ConvertUpdateToErrorResult(update, $"OrderPair {Name}: Base order failed/cancelled. Status: {update.Status}");
                    OnError?.Invoke(this, LastError);
                }
            }
            else if (isTpUpdate)
            {
                TakeProfitOrder = update;
                if (TakeProfitOrder.Status == OrderStatus.Filled)
                {
                    _log.Information($"OrderPair '{Name}': Take profit order filled. Triggering OnTakeProfitOrderFilled event.");
                    CalculatePnlAndFees();
                    // It is done in parent HedgeGridStrategy, so in case fails, ValidateStrategyState() always retries this at each ticker
                    RequestBaseOrderPlacement();
                    OnTakeProfitOrderFilled?.Invoke(this);
                }
                else if (BaseExchangeAPI.IsOrderInFinalState(TakeProfitOrder.Status))
                {
                    _log?.Warning($"OrderPair {Name}: TakeProfit order failed/cancelled. Status: {update.Status}, Reason: {update.RejectReason}");
                    LastError = ConvertUpdateToErrorResult(update, $"OrderPair {Name}: TakeProfit order failed/cancelled. Status: {update.Status}");
                    OnError?.Invoke(this, LastError);
                }
            }
            else
            {
                _log.Error($"*NOT SUPPOSE TO HAPPEN*: OrderPair '{Name}': Received update for an order that it's not actively managing. Ignoring.");
            }
        }

        public void RequestBaseOrderPlacement()
        {
            if (IsAwaitingResult)
            {
                _log?.Warning($"OrderPair {Name} Base Order placement skipped. Already awaiting result for order (BaseStatus: {BaseOrder?.Status}, TP Status: {TakeProfitOrder?.Status})");
                return;
            }
            // Allow placement IF:
            bool canPlace = BaseOrder == null ||
                            BaseOrder.Status == OrderStatus.Rejected ||
                            BaseOrder.Status == OrderStatus.Cancelled;

            if (!canPlace)
            {
                _log?.Debug($"OrderPoint {Name} Base Order placement skipped. Cannot place in current state (BaseStatus: {BaseOrder?.Status}, TP Status: {TakeProfitOrder?.Status}");
                return;
            }

            _log?.Information($"OrderPoint {Name} Requesting Base Limit Order @ {IntendedPrice}");

            var requestOrderType = _isInitialPair ? _strategyConfig.BaseOrderType : OrderType.Limit;  // Use config for initial pair, otherwise we force Limit
            decimal? requestPrice = null;
            if (requestOrderType == OrderType.Limit)
            {
                // Pseudocode plan:
                // 1. Use a random decimal between 0 and _strategyConfig.BaseOrderLimitPriceAdjustment for priceAdjustment.
                // 2. In DEBUG builds, always use 0 for priceAdjustment (for test determinism).
                // 3. In RELEASE builds, use the random value.
                // 4. Use #if DEBUG preprocessor directive to distinguish between Debug and Release at compile time.

                decimal priceAdjustment = _strategyConfig.BaseOrderLimitPriceAdjustment;

#if DEBUG
                priceAdjustment = 0m;
#else
                    if (_strategyConfig.BaseOrderLimitPriceAdjustment <= 0m)
                    {
                        priceAdjustment = 0m;
                    }
                    else
                    {
                        // System.Random is not thread-safe, but for this context it's fine.
                        var random = new Random();
                        // Generate a random decimal between 0 and BaseOrderLimitPriceAdjustment
                        priceAdjustment = (decimal)random.NextDouble() * _strategyConfig.BaseOrderLimitPriceAdjustment;
                    }
#endif
                requestPrice = IsLong ? IntendedPrice + priceAdjustment : IntendedPrice - priceAdjustment; // Adjust price based on direction to increase the chance of filling
            }
            var requestTimeInForce = _isInitialPair ? _strategyConfig.BaseOrderTimeInForce : TimeInForce.PostOnly; // Use config for initial pair, otherwise we force PostOnly
            var baseRequest = new FuturesOrderRequest()
            {
                ClientId = Guid.NewGuid().ToShortId(),
                IsBuy = IsLong,
                Amount = _tradingPair.RoundBaseAmount(_strategyConfig.StepSize),
                OrderType = requestOrderType,
                Price = requestPrice,
                TimeInForce = requestTimeInForce,
                IsReduceOnly = false,
                PositionDirection = IsLong ? PositionDirection.Buy : PositionDirection.Sell
            };

            // Create a NEW placeholder order model.
            BaseOrder = new OrderModelUpdate { ClientOrderId = baseRequest.ClientId, Status = OrderStatus.Created };
            OnOrderPlacementRequest?.Invoke(this, baseRequest);
        }

        public void RequestTakeProfitOrderPlacement()
        {
            if (IsAwaitingResult)
            {
                _log?.Warning($"OrderPair {Name} Take Profit Order placement skipped. Already awaiting result for order (BaseStatus: {BaseOrder?.Status}, TP Status: {TakeProfitOrder?.Status})");
                return;
            }
            // Allow placement IF: no TP order exists or previous one failed - TODO check if this logic is correct
            bool canPlace = TakeProfitOrder == null ||
                            TakeProfitOrder.Status == OrderStatus.Rejected ||
                            TakeProfitOrder.Status == OrderStatus.Cancelled;
            if (!canPlace)
            {
                _log?.Debug($"OrderPoint {Name} Take Profit Order placement skipped. Cannot place in current state (BaseStatus: {BaseOrder?.Status}, TP Status: {TakeProfitOrder?.Status}");
                return;
            }
            var takeProfitPrice = CalculateTakeProfitPrice(); // already rounds properly
            if (takeProfitPrice == null)
            {
                _log?.Warning($"OrderPair '{Name}': Cannot place take profit order. Base order not filled or invalid price.");
                return;
            }
            var requestOrderType = _isInitialPair ? _strategyConfig.TakeProfitOrderType : OrderType.Limit;  // Use config for initial pair, otherwise we force Limit
            var requestTimeInForce = _isInitialPair ? _strategyConfig.TakeProfitOrderTimeInForce : TimeInForce.PostOnly; // Use config for initial pair, otherwise we force PostOnly
            var requestPrice = requestOrderType == OrderType.Limit ? takeProfitPrice : (decimal?)null; // Only set Price for Limit orders
            var requestAmount = BaseOrder?.QuantityFilled ?? 0m;
            var tpRequest = new FuturesOrderRequest()
            {
                ClientId = Guid.NewGuid().ToShortId(),
                IsBuy = !IsLong, // Opposite of base order
                Amount = requestAmount, //_tradingPair.RoundBaseAmount(requestAmount),
                OrderType = requestOrderType,
                Price = requestPrice,
                TimeInForce = requestTimeInForce,
                IsReduceOnly = true,
                PositionDirection = IsLong ? PositionDirection.Buy : PositionDirection.Sell
            };
            // Create a NEW placeholder order model.
            TakeProfitOrder = new OrderModelUpdate { ClientOrderId = tpRequest.ClientId, Status = OrderStatus.Created };
            OnOrderPlacementRequest?.Invoke(this, tpRequest);
        }

        public void ReconstructOrders(OrderModelUpdate? reconstructedBaseOrder, OrderModelUpdate? reconstructedTpOrder)
        {
            _log.Information($"OrderPair '{Name}': Reconstructing state. Base: {reconstructedBaseOrder?.ClientOrderId ?? reconstructedBaseOrder?.OrderId}, TP: {reconstructedTpOrder?.ClientOrderId ?? reconstructedTpOrder?.OrderId}");
            if (reconstructedBaseOrder == null && reconstructedTpOrder == null)
            {
                _log.Warning($"OrderPair '{Name}': Both reconstructed orders are null. Cannot reconstruct state.");
                return;
            }
            BaseOrder = reconstructedBaseOrder;
            TakeProfitOrder = reconstructedTpOrder;
        }

        public decimal? CalculateTakeProfitPrice()
        {
            if (!IsBaseOrderFilled || !(BaseOrder?.AveragePrice.HasValue ?? false))
                return null;

            var calcTakeProfitPrice = IsLong ? BaseOrder.AveragePrice.Value + _strategyConfig.ProfitTargetDistance : BaseOrder.AveragePrice.Value - _strategyConfig.ProfitTargetDistance;

            if (calcTakeProfitPrice <= 0)
            {
                return null;
            }
            return _tradingPair.RoundQuoteAmount(calcTakeProfitPrice);
        }


        // Helper function to convert OrderModelUpdate to OrderResult for OnError event
        private OrderResult ConvertUpdateToErrorResult(OrderModelUpdate update, string defaultMessage)
        {
            return new FuturesOrderResult
            {
                IsSuccess = false,
                Message = update.RejectReason ?? defaultMessage,
                OrderId = update.OrderId ?? string.Empty,
                ClientOrderId = update.ClientOrderId,
                Status = update.Status, // Pass the final failed/cancelled status
                Timestamp = update.UpdateTime
            };
        }
    }
}

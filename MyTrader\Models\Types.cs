using System;
using System.Text.Json.Serialization;

namespace MyTraderSpace.Models
{
    // Just example:
    // Attribute definition, optionally allow targets and parameters
    [AttributeUsage(AttributeTargets.Class)]
    public class ExchangeTagAttribute : Attribute
    {
        public string Name { get; }

        public ExchangeTagAttribute(string name)
        {
            Name = name;
        }
    }

    // Apply to classes
    [ExchangeTag("Bybit")]
    public class ByBit { }

    [ExchangeTag("Simulated")]
    public class Simulated { }
    public class Binance { }
    public class Mexc { }
    public class BitMart { }

    // Usage Example:
    //Type t = typeof(ByBit);
    //var attr = (ExchangeTagAttribute)Attribute.GetCustomAttribute(t, typeof(ExchangeTagAttribute));
    //if (attr != null)
    //{
    //    Console.WriteLine($"Exchange name: {attr.Name}");
    //}

    public enum ExchangeType
    {
        Simulated,
        Bybit,
        Binance,
        Mexc,
        BitMart
        // Add other exchanges as needed
    }

    public enum CoinType
    {
        BTC,
        LTC,
        ETH,
        USDT,
        USDC,
        XRP
    }

    public enum AccountType
    {
        Spot,
        Margin,
        Futures,
        Unified,  // For exchanges like Bybit that support unified margin
        Options,  // For future expansion
        Funding   // For future expansion
    }

    public enum ExchangeState 
    {
        Initializing,
        Connected,
        Ready,     // State when the exchange is ready for trading
        Running,
        Stopping,
        Stopped,
        Error,
        Disposed
    }

    public enum StrategyState
    {
        Initializing,
        Ready,
        Running,
        Stopping,
        Stopped,
        Error
    }

    public enum TradeMode
    {
        CrossMargin,
        Isolated
    }

    public enum Category
    {
        Linear,
        Inverse,
        Spot,
        Option,
        Undefined
    }

    public enum OrderSide
    {
        Buy,
        Sell
    }

    public enum OrderType 
    {
        Market,
        Limit
    }

    public enum TimeInForce
    {
        GoodTillCancel,
        ImmediateOrCancel,
        FillOrKill,
        PostOnly
    }

    public enum PositionMode
    {
        MergedSingle,  // One-Way Mode
        BothSides      // Hedge Mode
    }

    public enum PositionStatus
    {
        Normal,
        Liqidation,
        AutoDeleverage
    }

    public enum OrderStatus
    {
        Created,
        New,
        Rejected,
        PartiallyFilled,
        Filled,
        Cancelled,
        //PendingCancel, // we might not use this
        Untriggered
    }

    public enum PositionDirection
    {
        OneWay,  // Position in One-Way Mode
        Buy,     // Long position in Hedge Mode
        Sell     // Short position in Hedge Mode
    }

    public enum PositionSide
    {
        Buy,     // Long position
        Sell,    // Short position
        None     // No position
    }

    public enum MarginMode
    {
        RegularMargin,
        PortfolioMargin,
        IsolatedMargin
    }

    [JsonConverter(typeof(CurrencyPairConverter))]
    public readonly struct CurrencyPair
    {
        public CoinType BaseCoin { get; }
        public CoinType QuoteCoin { get; }
        public int BasePrecision { get; }
        public int QuotePrecision { get; }

        public CurrencyPair(CoinType baseCoin, CoinType quoteCoin, int basePrecision, int quotePrecision)
        {
            if (basePrecision < 0)
                throw new ArgumentException("Base precision cannot be negative", nameof(basePrecision));
            if (quotePrecision < 0)
                throw new ArgumentException("Quote precision cannot be negative", nameof(quotePrecision));
            if (baseCoin == quoteCoin)
                throw new ArgumentException("Base and quote coins must be different");

            BaseCoin = baseCoin;
            QuoteCoin = quoteCoin;
            BasePrecision = basePrecision;
            QuotePrecision = quotePrecision;
        }

        public string Symbol => $"{BaseCoin}{QuoteCoin}";

        public decimal RoundBaseAmount(decimal amount) =>
            Math.Round(amount, BasePrecision, MidpointRounding.ToZero);

        public decimal RoundQuoteAmount(decimal amount) =>
            Math.Round(amount, QuotePrecision, MidpointRounding.ToZero);

        public override string ToString() => Symbol;
    }

    public readonly struct CurrencyAmount
    {
        public CoinType Currency { get; }
        public decimal Amount { get; }

        public CurrencyAmount(CoinType currency, decimal amount)
        {
            if (amount < 0)
                throw new ArgumentException("Amount cannot be negative", nameof(amount));

            Currency = currency;
            Amount = amount;
        }

        public CurrencyAmount WithAmount(decimal newAmount) => 
            new(Currency, newAmount);

        public static CurrencyAmount operator +(CurrencyAmount a, CurrencyAmount b)
        {
            if (a.Currency != b.Currency)
                throw new InvalidOperationException("Cannot add amounts of different currencies");

            return new CurrencyAmount(a.Currency, a.Amount + b.Amount);
        }

        public override string ToString() => $"{Amount:G29} {Currency}";
    }

    public readonly struct Fee
    {
        public CurrencyAmount Base { get; }
        public CurrencyAmount Quote { get; }

        public Fee(CurrencyPair currencyPair)
        {
            Base = new CurrencyAmount(currencyPair.BaseCoin, 0);
            Quote = new CurrencyAmount(currencyPair.QuoteCoin, 0);
        }

        public Fee(CurrencyAmount baseAmount, CurrencyAmount quoteAmount)
        {
            Base = baseAmount;
            Quote = quoteAmount;
        }

        public static Fee operator +(Fee a, Fee b)
        {
            if (a.Base.Currency != b.Base.Currency || a.Quote.Currency != b.Quote.Currency)
                throw new InvalidOperationException("Cannot add fees with different currencies");

            return new Fee(
                a.Base + b.Base,
                a.Quote + b.Quote
            );
        }

        public Fee RoundTo(CurrencyPair pair) => new(
            Base.WithAmount(pair.RoundBaseAmount(Base.Amount)),
            Quote.WithAmount(pair.RoundQuoteAmount(Quote.Amount))
        );

        public override string ToString() =>
            $"Base: {Base}, Quote: {Quote}";
    }

    // Common currency pairs with their typical precisions
    public static class CommonPairs
    {
        public static readonly CurrencyPair BTCUSDT = new(CoinType.BTC, CoinType.USDT, 6, 2);
        public static readonly CurrencyPair ETHUSDT = new(CoinType.ETH, CoinType.USDT, 5, 2);
        public static readonly CurrencyPair LTCUSDT = new(CoinType.LTC, CoinType.USDT, 5, 2);
        public static readonly CurrencyPair XRPUSDT = new(CoinType.XRP, CoinType.USDT, 1, 2);

        /// <summary>
        /// Parses a symbol string (like "BTCUSDT") into a CurrencyPair
        /// </summary>
        /// <param name="symbol">The symbol to parse</param>
        /// <returns>A CurrencyPair representing the symbol</returns>
        /// <exception cref="ArgumentException">Thrown when the symbol format is invalid or contains unknown coins</exception>
        public static CurrencyPair ParseSymbolToCurrencyPair(string symbol)
        {
            // First check if it's one of the common pairs
            if (symbol == BTCUSDT.Symbol)
                return BTCUSDT;
            if (symbol == ETHUSDT.Symbol)
                return ETHUSDT;
            if (symbol == LTCUSDT.Symbol)
                return LTCUSDT;
            if (symbol == XRPUSDT.Symbol)
                return XRPUSDT;
            
            // If not a common pair, try to parse it
            // Assuming the last 4 characters are the quote currency and the rest is the base currency
            if (symbol.Length <= 4)
                throw new ArgumentException($"Invalid symbol format: {symbol}");
            
            string baseCoinStr = symbol.Substring(0, symbol.Length - 4);
            string quoteCoinStr = symbol.Substring(symbol.Length - 4);
            
            if (!Enum.TryParse<CoinType>(baseCoinStr, out var baseCoin))
                throw new ArgumentException($"Unknown base coin: {baseCoinStr}");
            
            if (!Enum.TryParse<CoinType>(quoteCoinStr, out var quoteCoin))
                throw new ArgumentException($"Unknown quote coin: {quoteCoinStr}");
            
            // Use default precisions for non-common pairs
            return new CurrencyPair(baseCoin, quoteCoin, 6, 2);
        }
    }
} 
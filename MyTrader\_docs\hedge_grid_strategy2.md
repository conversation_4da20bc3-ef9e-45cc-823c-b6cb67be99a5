# Hedge Grid Strategy Description

This document aims to further clarify what is already stated in hedge_grid_strategy.md, providing a more detailed description of the Hedge Grid trading strategy.

## Overview

The strategy aims to profit from price volatility in perpetual futures markets (specifically BTC/USDT initially). It operates under the assumption that the exchange account is configured for **Hedge Mode (BothSides)**, allowing simultaneous long and short positions for the same contract.

    ## Core Logic

**Description of trading flow**

At the start of the application, the grid is attempted to be reconstructed using the position and order datas residing in each BaseExchangeAPI instance in ApiPool (FetchPositions and FetchOrders)
At the end of the reconstruction two results can happen: either there was 'not blank' ExchangeAPI elements with found positions or orders, 
and it was possible to reconstruct anything, or no BaseExchangeAPI element had any positions or orders, so no elements could be constructed to the MainsTragegy->_strategyPool
First we start with the case of initial 'blank slate' case
# 1. Initial 'Blank Slate' case:
  1.1. There is an initial futures ticker bid and ask price. This comes from the _marketDataService MainStrategy was constructed with.
The first point/step is placed "as fast as possible" meaning, for now, we just place straight 2 opposite sided market orders.
This means we construct an intial HedgeGridStrategy, and as soon as it is creted it is assigned to _currentStrategy 'frontier' strategy AND 
also we subscribe to it's HedgridStrategy.OnMarketDataUpdate event (line 59 in HedgeGridStrategy.cs) for futures ticker price updates.
When the 2 orders are filled, we average the entry prices, and that will be the IntendedPrice for the first step.
**New thing**: Making sure we properly set "true first time both BaseOrders filled" sub-strategies, so in HedgeGridStrategy we have:
 private bool _internal_IsWasBothBaseOrdersFilledFirstTime = false; // this can be true only once during the lifetime of the strategy! (and never false again)
 public bool IsWasBothBaseOrdersFilledFirstTime { get; private set; } = false; // this can be true only once during the lifetime of the strategy! (and never false again)
 ...
 HedgeGridStrategy.private void HandleBaseOrderPairFilled(OrderPair sender)
 {
    if (LongSide.IsBaseOrderFilled && ShortSide.IsBaseOrderFilled)
    {
      if (_internal_IsWasBothBaseOrdersFilledFirstTime == false)
      {
        _internal_IsWasBothBaseOrdersFilledFirstTime = true; // will be set true only once, never again
        IsWasBothBaseOrdersFilledFirstTime = true;
       }
       else // if was true, IsWasBothBaseOrdersFilledFirstTime will not be true ever again
       {
          IsWasBothBaseOrdersFilledFirstTime = false;
          _log.Debug($"[{NameId}] Both base orders filled. IsWasBothBaseOrdersFilled set to true.");
       }
     }
     OnBaseOrderPairFilled?.Invoke(this, sender); // forward to MainStrategy
 }
 MainStrategy.private async void HandleBaseOrderPairFilledAsync(HedgeGridStrategy strategy, OrderPair filledPair)
 {
     if (strategy.LongSide.IsBaseOrderFilled && strategy.ShortSide.IsBaseOrderFilled)
     {
         await PruneDistantStrategiesAsync(); // now this simply consolidates/closes all the IsActive() == false strategies, it suppose to be found all the time just one (distant checking is (just) assurance)
         if (strategy.IsWasBothBaseOrdersFilledFirstTime)
         {
             _currentStrategy.OnMarketDataUpdate -= OnMainStrategyFuturesMarketDataUpdate;
             _currentStrategy = strategy; // we can be sure this is 'frontier' strategy
             _currentStrategy.OnMarketDataUpdate += OnMainStrategyFuturesMarketDataUpdate;
         }
         // ... rest of the code creating the upper and lower 2 new strategies ...
         // one at currentIntendedPrice + StepSize, and the other one is with currentIntendedPrice - StepSize
         // of course added the BaseOrderLimitPriceAdjustment +/- to the limit price to increase the chance of getting filled by having a bet deeper in the orderbook
     }
 }
 1.2. Next, any BaseOrder fillment triggers the placement of the appropriate TakeProfitOrders, and vice-versa.
 This can happen on OrderPair.cs level on the filled events, or in HedgeGridStrategy's ValidateStrategyState() which is checked at every ticker update.
 Normally at both places could be present this (hopefully we are properly guarded with the IsAwaitingResult/IsAnySideAwaitingResult)
 Note: Apart from the very intital slate base orders which are market orders, we all the time try to force to put PostOnly limit orders for both BaseOrders and TakeProfitOrders
 since they are at (some substantial) `StepSize` and/or `ProfitTargetDistance` away from the IntendedPrice base price, so hopefully they fit into orderbook as PostOnly.
 1.3. After the creation of these 2 new upper and lower (HedgeGridStrategy sub)strategies they are at this point IsActive() == false we just wait and do nothig
 and for the (ticker) price to move `StepSize` away from the IntendedPrice of upper or lower new strategies. 
 This will cause to trigger *one of the* just previously placed 2 new HedgeGridStrategy sub-strategies BaseOrders to fill.
 When both BaseOrders fills, and IsWasBothBaseOrdersFilledFirstTime is true, this is the same situation as discussed above in "**New Thing**", pruning happens, 2 new strategies created, etc ...
 1.4. So far we are 100 USDT distance (StepSize) from the very first IntendedPrice, and let's say the price continues to move in the same direction, 
 reaching 200 USDT disntance (`ProfitTargetDistance`) from the very first IntendedPrice. At this moment the appropriate PostOnly AND ReduceOnly !! TakeProfit order fills.
 This immediately triggers the re-placement of the 'original' BaseOrder pair to this TakeProfit order just filled, in PostOnly limit way (but NOT reduceOnly, because Only TakeProfitOrders are ReduceOnly)
 1.5. Also since we are again 100 USDT distance from the previous 100 USDT distance, this qualifies as condition to trigger the fullfillment of one of the new HedgeGridStrategy sub-strategies.
 For better clarity, let's call the very-first intitial market sub-strategy as Level 1, the 2 created sub-strategies as Level 2, and the 2 created strategies from the Level 2 as Level 3.
 So we are currently at 100 + 100 distance wich corresponds to Level 3, so the Level3 sub-strategies BaseOrders are filled, and the proper above discussed things happens continuously ...
 1.6. So the main point is that the fullfillment of TakeProfit orders triggers the re-placement of the appropriate BaseOrders, and the fullfillment of BaseOrders triggers the re-placement of TakeProfit orders
 AND *if* and **ONLY IF** the IsWasBothBaseOrdersFilledFirstTime is true, then the pruning of distant strategies happens, and the creation of new sub-strategies happens!!! otherwse just the TakeProfit orders are re-placed.
 
 # 2. Reconstruction case
 The problem with the reconstruction is, that although we can iterate through all the ApiPool BaseExchangeAPI elements,
 we can Fetch the positions and orders, and if we find any we call the OrderPair.ReconstructOrders inside HedgeGridStrategy.ReConstructStateAsync()
 these clearly would consist as "in-depth points" of the grid, and those BaseExchangeAPI elements which has no positions open, but only 2 pending orders,
 those would be surely the "frontier" IsActive() == false points (suppose to exist only 2 all the time) but the problem lies in detemining/finding
 the "IsWasBothBaseOrdersFilledFirstTime" point ... we could say that "find that point which is between the 2 "frontier" pending points (IsActive() == false points)"
 but that unfortunately not enough condition ... multiple "deeper" points could satisfy that condition ...
 So this means we have to rely on the BaseExchangeAPI's TimeStamps ... CreatedTimes and UpdatedTimes of the positions and orders.
 So when reconstructing we do the following:
 2.1. We iterate through all the ApiPool BaseExchangeAPI elements, and for each of them we FetchPositions and FetchOrders.
 2.2. Find the highest valued TimeStamp from any position or order, that will be the "markTimeStamp" of the element. (or reconstructTimeStamp maybe would be more descriptive)
 2.3. Order the BaseExchangeAPI elements in increasing order of their reconstructTimeStamp.
 2.4. Make the actual reconstruction in that order.
 2.5. The last reconstructed 2 element should be "frontier" IsActive() == false elements ... we take the right previous element's reconstructTimeStamp 
 which is not ("frontier"/IsActive() == false(so it is true)) as the "IsWasBothBaseOrdersFilledFirstTime" point.
 This should conclude the proper reconstruction of the grid MainStrategy's _strategyPool elements.
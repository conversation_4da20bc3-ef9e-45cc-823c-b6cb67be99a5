﻿# This is the list of problems needed to be solved in the current - not-ready/sketch/partially legacy codes - in order to implement the acrchitecture described by new_architecture.md

## I. Core Architecture & Strategy Refactoring (`MainStrategy` & `HedgeGridStrategy`)

1.  **`MainStrategy` Refinement:**
    *   **Problem:** `MainStrategy.cs` currently has placeholder logic for API and strategy pool management and sub-strategy instantiation. It needs to be fully implemented according to `new_architecture.md`.
    *   **ToDo:**
        *   Implement robust `_apiPool` (received from `ExchangeTrader`) and `_strategyPool` (internal to `MainStrategy`) management (add, remove, acquire API from `_apiPool`, release API to `_apiPool`).
        *   Implement logic in `MainStrategy.InitializeAsync` to:
            *   Receive the list of `BaseExchangeAPI` instances (the "pool") from `ExchangeTrader`.
            *   Load its own configuration (e.g., `MainStrategyConfig.json` - see point 4 & 12) to determine which named APIs from the `ExchangeTrader` pool to use.
            *   **State Reconstruction/Initial Population of `_strategyPool` (Note 3):**
                *   Iterate through each `BaseExchangeAPI` instance designated for use (from its config).
                *   For each API, attempt to create and initialize a `HedgeGridStrategy` instance. The `HedgeGridStrategy.InitializeAsync()` method will be responsible for fetching its own positions/orders via its assigned API and reconstructing its state (see point 3).
                *   If a `HedgeGridStrategy` initializes successfully (meaning it reconstructed a valid state or is ready for a new blank state step), add it to `MainStrategy._strategyPool`. If it fails to initialize or determines it has no valid state/purpose (e.g., no positions/orders on a "non-blank" API), it should not be added.
                *   If, after iterating through all designated APIs, the `_strategyPool` is empty (i.e., no existing grid steps could be reconstructed and no APIs are suitable for a "blank state" start *if that's a separate consideration*), then `MainStrategy` proceeds with the "First Step Special Case" logic (see point 2) using one of the available/suitable APIs.
        *   Modify `MainStrategy.InitiateNewTradePointAsync` (to be renamed, e.g., `CreateNewHedgeGridStepAsync` or similar) to be used when expanding the grid *after* initial setup/reconstruction:
            *   Acquire an available `BaseExchangeAPI` from its pool.
            *   Acquire the appropriate `IMarketDataService`.
            *   Instantiate a new `HedgeGridStrategy` with the API, market service, `HedgeGridStrategyConfig`, and the calculated `intendedPrice` for the new step.
            *   Call `InitializeAsync()` on the new `HedgeGridStrategy`.
            *   Add the new `HedgeGridStrategy` to its `_strategyPool` if initialization is successful.
        *   Implement the `OnStrategyRemovalRequest` handler in `MainStrategy` to release the `BaseExchangeAPI` back to its pool when a `HedgeGridStrategy` is removed/completed.
        *   **Market Data Subscription (Note 3 Addendum):**
            *   During `MainStrategy.InitializeAsync`, after attempting to populate `_strategyPool` from existing states:
                *   If `_strategyPool` is not empty, temporarily subscribe to the `OnMarketDataUpdate` of the first successfully initialized `HedgeGridStrategy`.
                *   Once all APIs have been processed and `_strategyPool` is fully populated (or the first step is created on a blank slate):
                    *   Determine the "active" `HedgeGridStrategy` (e.g., the one whose `IntendedPrice` is closest to the current market price from the temporary subscription).
                    *   Unsubscribe from the temporary subscription and re-subscribe `MainStrategy.OnFuturesMarketDataUpdate` to this "active" `HedgeGridStrategy`'s `OnMarketDataUpdate` event.
                *   This "active" subscription should be re-evaluated and potentially moved each time a new `HedgeGridStrategy` is created and becomes the new price frontier.

2.  **`HedgeGridStrategy` Full Implementation (adapting `OrderPair`, removing `OrderPoint`/`TradePoint`):**
    *   **Problem:** `HedgeGridStrategy.cs` needs a complete overhaul to implement the logic described in `hedge_grid_strategy.md`, using a refactored `OrderPair.cs`.
    *   **ToDo:**
        *   Remove dependencies on `OrderPoint` and `TradePoint` from `HedgeGridStrategy.cs`.
        *   Refactor/adapt `OrderPair.cs`. A `HedgeGridStrategy` instance manages *one step* of the grid. It will have two `OrderPair` instances: `LongSide` and `ShortSide` (already present as properties - Note 1). Each `OrderPair` manages one side of the hedge for that step (e.g., the long base order and its corresponding TP order).
        *   Implement the "Core Logic" / "Description of trading flow" from `hedge_grid_strategy.md` within `HedgeGridStrategy.cs` (and its `OrderPair` instances). This includes:
            *   **Initial Step Placement (First Step Special Case - invoked by `MainStrategy` if a blank slate, see point 3):**
                *   The `HedgeGridStrategy` (via its `LongSide` and `ShortSide` `OrderPair`s) will place hedged short and long orders (PostOnly limit, then market if fails after attempts).
                *   It will calculate its `IntendedPrice` from averaged fill prices.
                *   It will place two TakeProfitOrders (PostOnly, ReduceOnly) via its `OrderPair`s.
                *   After its own base orders are filled and TPs are placed, it signals `MainStrategy` (e.g., via an event or return status) that it's ready, which then triggers `MainStrategy` to evaluate creating two new `HedgeGridStrategy` instances (for `currentIntendedPrice + StepSize` and `currentIntendedPrice - StepSize`).
            *   **Subsequent Step Logic (when BaseOrders of a newly created `HedgeGridStrategy` fill):**
                *   When a `HedgeGridStrategy`'s base orders (both long and short for its step, managed by `OrderPair`s) are filled:
                    *   It attempts to place its corresponding TakeProfit orders (via `OrderPair`s).
                    *   It signals `MainStrategy` (e.g. completion of its own initialization/activation), which then may cancel other now-distant `HedgeGridStrategy` instances.
            *   **Take-Profit Fill Logic (managed by an `OrderPair`):**
                *   When a TakeProfit order fills:
                    *   The `OrderPair` should immediately re-place its BaseOrder (PostOnly limit, NOT ReduceOnly).
                    *   The parent `HedgeGridStrategy` is notified of this TP fill. `MainStrategy` is responsible for checking if new steps are needed based on price movement relative to the overall grid structure (Note 2).
        *   Define and use `HedgeGridStrategyConfig.cs` for all its parameters (StepSize, PositionSizePerStep, etc.).
        *   Ensure correct use of `OrderType` (Market/Limit), `TimeInForce` (PostOnly, FillOrKill, GTC), and `reduceOnly` flags as per `hedge_grid_strategy.md` and user's Note 2. These are primarily for fallback if PostOnly limit fails.
        *   Implement `BaseOrderLimitPriceAdjustment`.
        *   Implement `StopOnLiquidationPriceRatio` logic (decide if stop means cancel & release or just do nothing).
        *   Implement `MaxInitialStepSpreadTolerancePercentage` for reopening/trade decisions.
        *   The `OnMarketDataUpdate` event is already defined. Ensure it's invoked correctly when the strategy receives relevant data from its `IMarketDataService`.

3.  **State Management & Persistence for `HedgeGridStrategy` via Reconstruction:**
    *   **Problem:** The state of multiple `HedgeGridStrategy` instances (representing the grid) needs to be re-established on startup.
    *   **Clarification (User Note 4 & 3):** `HedgeGridStrategy.InitializeAsync()` will handle its own state reconstruction.
    *   **ToDo:**
        *   **`HedgeGridStrategy.InitializeAsync()`:**
            *   This method will call `FetchPositionsAsync()` and `FetchOrdersAsync()` using its assigned `_exchangeAPI`.
            *   Based on the fetched positions and orders, it will reconstruct its internal state, primarily configuring its `LongSide` and `ShortSide` `OrderPair` instances.
            *   The logic (inspired by the old `ReConstructState` comment) will be:
                *   If fetched positions for its designated symbol/side are non-zero, it means its base orders were filled. Any relevant pending orders are expected to be its TakeProfit orders.
                *   If fetched positions are zero, any relevant pending orders are expected to be its initial BaseOrders.
                *   If no relevant positions or orders are found for its expected step (e.g., if `MainStrategy` assigned it an API that *should* have a step but doesn't), `InitializeAsync` might return a failure or a "blank" status, allowing `MainStrategy` to decide if this `HedgeGridStrategy` instance should be kept or discarded.
            *   Set its `IntendedPrice` based on reconstructed filled positions if possible, or expect it to be set by `MainStrategy` if it's a new step.
        *   **Update `hedge_grid_strategy.md`:** Reflect this reconstruction approach. The "first step is a somewhat special case" applies *only if* `MainStrategy` determines a complete blank slate across all its designated APIs during its own initialization. Otherwise, the grid is reconstructed from existing exchange state.

4.  **Configuration Files Definition:**
    *   **Problem:** The exact structure and parameters for `MainStrategyConfig.json` and the refined `HedgeGridStrategyConfig.cs` need to be finalized.
    *   **ToDo:**
        *   Define `MainStrategyConfig.json` (or section in `strategies.json`):
            *   `apiNamesToUse`: (string array) List of `BaseExchangeAPI.Name` strings that `MainStrategy` should request from `ExchangeTrader`'s pool.
            *   `maxActiveStrategies`: (int) Maximum number of concurrent `HedgeGridStrategy` instances. `0` means use all available APIs from its `apiNamesToUse` list. (See point 15)
            *   Other global risk or strategy-level parameters for `MainStrategy`.
        *   Finalize `HedgeGridStrategyConfig.cs`: Ensure all parameters from `hedge_grid_strategy.md` are present (StepSize, PositionSizePerStep, ProfitTargetDistance, Leverage, BaseOrderTimeInForce, TakeProfitOrderTimeInForce, etc.), EXCLUDING `Symbol` and `MaxActiveTrades`.
        *   Update `ConfigurationLoader.cs` to correctly load and parse these new/updated config structures.

## II. API Layer (`BaseExchangeAPI` & Concrete Implementations)

5.  **Leverage Handling in `BaseExchangeAPI` and `BybitExchangeAPI`:**
    *   **Problem:** The `hedge_grid_strategy.md` mentions a single "Leverage (e.g., 100x)".
    *   **Clarification (User Note 5):** The same leverage will be used for Long and Short.
    *   **ToDo:**
        *   Ensure `BybitExchangeAPI.SetLeverageAsync` correctly applies the single leverage value. (Confirm with tests).
        *   `ExchangeConfig.Leverage` is the source.

6.  **Order Placement Validation (`ValidatePositionDirectionAndMode`):**
    *   **Problem:** Usage of `BaseExchangeAPI.ValidatePositionDirectionAndMode`.
    *   **Clarification (User Note 6):** Likely okay.
    *   **ToDo:** Mark for confirmation during testing.

7.  **`PositionMode` Synchronization:**
    *   **Problem:** `HedgeGridStrategy` requires `PositionMode.BothSides`.
    *   **Clarification (User Note 7):** Bybit V5 API may not allow fetching all `AccountSettings`. `PositionMode` is configured via `config.json` and `MainStrategy` ensures APIs attempt to set this.
    *   **ToDo:**
        *   Retain logic in `BybitExchangeAPI.InitializeAsync` to *attempt* to set `PositionMode` from its `_config`.
        *   Add code comments in `BybitExchangeAPI.GetAccountSettingsAsync` regarding Bybit's V5 API limitation for fetching this setting.

8.  **Order Final State Handling in `WaitForOrderStatusAsync`:**
    *   **Problem:** Robustness of `BaseExchangeAPI.WaitForOrderStatusAsync`.
    *   **Clarification (User Note 8):** Worked previously.
    *   **ToDo:** Keep for review/confirmation during integration testing.

## III. General Code Quality & Refinements

9.  **Logging Consistency and Verbosity:**
    *   **Problem:** Ensure clear and sensible logging.
    *   **Clarification (User Note 9):** Avoid overly verbose logs.
    *   **ToDo:** Review and ensure sensible logging levels.

10. **Asynchronous Operations (`fire_and_forget_forbidden.md`):**
    *   **Problem:** Adherence to "no fire-and-forget".
    *   **Clarification (User Note 10):** Reaffirm.
    *   **ToDo:** Double-check async code.

11. **Error Handling and Resilience:**
    *   **Problem:** Strategy resilience.
    *   **Clarification (User Note 11):** `IsAnySideAwaitingResult` (in `HedgeGridStrategy`/`OrderPair`) is a key safeguard. Retries are implicit.
    *   **ToDo:** Implement try-catch blocks. The `IsAnySideAwaitingResult` check in `HedgeGridStrategy` (and its `OrderPair`s) should prevent concurrent conflicting operations.

12. **Configuration of `ExchangeTrader` and `MainStrategy`:**
    *   **Problem:** Finalize configuration details.
    *   **Clarification (User Note 12):** `MainStrategy` uses `BaseExchangeAPI.Name`. `maxActiveStrategies` is in `MainStrategyConfig`.
    *   **ToDo:** (Covered by point 4)

13. **Cleanup of Legacy Code:**
    *   **Problem:** `OrderPoint.cs` and `TradePoint.cs` are legacy.
    *   **Clarification (User Note 13):** `OrderPoint.cs` and `TradePoint.cs` to be removed. `OrderPair.cs` adapted.
    *   **ToDo:** Remove `OrderPoint.cs`, `TradePoint.cs`. Refactor/integrate `OrderPair.cs` into `HedgeGridStrategy.cs`.

14. **`HedgeGridStrategyConfig.Symbol`:**
    *   **Problem:** `Symbol` in `HedgeGridStrategyConfig`.
    *   **Clarification (User Note 14):** `Symbol` comes from `BaseExchangeAPI`.
    *   **ToDo:** Remove `Symbol` from `HedgeGridStrategyConfig.cs`.

15. **`HedgeGridStrategyConfig.MaxActiveTrades` vs. `MainStrategyConfig.maxActiveStrategies`:**
    *   **Problem:** Location of this config.
    *   **Clarification (User Note 15 & 1):** `maxActiveStrategies` in `MainStrategyConfig`.
    *   **ToDo:** Remove `MaxActiveTrades` from `HedgeGridStrategyConfig.cs`, add `maxActiveStrategies` to `MainStrategyConfig` (see point 4).

16. **`IUIDataProvider` and `StrategyUIData.cs` for `HedgeGridStrategy`:**
    *   **Problem:** UI data provision.
    *   **Clarification (User Note 16):** Lower priority.
    *   **ToDo:** Basic status in `HedgeGridStrategy.GetUIDataSnapshot()`. `MainStrategy` aggregates or selects.

## IV. Testing and Validation

17. **Unit and Integration Tests:**
    *   **Problem:** Need thorough testing.
    *   **Clarification (User Note 17):** `TraderTests` project exists.
    *   **ToDo:** Plan and write tests.

This list should provide a good starting point for our refactoring efforts.

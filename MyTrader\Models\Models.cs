namespace MyTraderSpace.Models
{
    // TODO: Check where elsewhere this could/should be used!
    public class OperationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    // Base class for all order results
    public class OrderResult
    {
        public bool IsSuccess { get; init; }
        public string OrderId { get; init; } = string.Empty;
        public string? ClientOrderId { get; init; } = null;
        public decimal? ExecutedPrice { get; init; } = null;
        public decimal? ExecutedQuantity { get; init; } = null;
        public OrderStatus Status { get; init; }
        public DateTime Timestamp { get; init; }
        public string Message { get; set; } = string.Empty;
    }

    // Spot order specific result
    public class SpotOrderResult : OrderResult
    {
        // Any spot-specific properties would go here
    }

    // Futures order specific result
    public class FuturesOrderResult : OrderResult
    {
        public decimal Leverage { get; init; }
    }

    // TODO: This is not used, check where it should be used
    public class SpotOrderRequest
    {
        public string? ClientId { get; init; } = null;
        public bool IsBuy { get; init; } = true;
        public decimal Amount { get; init; }
        public decimal? Price { get; init; }
        public decimal? Leverage { get; init; } = null;
        public OrderType OrderType { get; init; } = OrderType.Market;
        public TimeInForce TimeInForce { get; init; } = TimeInForce.PostOnly;
    }

    public class FuturesOrderRequest : SpotOrderRequest
    {
        PositionMode PositionMode { get; init; } = PositionMode.BothSides;
        public bool IsReduceOnly { get; init; } = false;

        private PositionDirection? _positionDirection = null;
        public PositionDirection? PositionDirection
        {
            get
            {
                if (_positionDirection == null)
                {
                    if (PositionMode == PositionMode.BothSides)
                        _positionDirection = (IsBuy ? Models.PositionDirection.Buy : Models.PositionDirection.Sell);
                }
                return _positionDirection;
            }
            set
            {
                _positionDirection = value;
            }
        }
    }

    public interface IUpdatable<T>
    {
        void Update(T newData);
    }

    public class SpotMarketData
    {
        public string Symbol { get; set; } = string.Empty;
        public decimal LastPrice { get; set; }
        public decimal HighestBid { get; set; }
        public decimal LowestAsk { get; set; }
        public decimal Volume24h { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public SpotMarketData()
        {

        }

        // Copy constructor
        public SpotMarketData(SpotMarketData spotMarketData)
        {
            Symbol = spotMarketData.Symbol;
            LastPrice = spotMarketData.LastPrice;
            HighestBid = spotMarketData.HighestBid;
            LowestAsk = spotMarketData.LowestAsk;
            Volume24h = spotMarketData.Volume24h;
            Timestamp = spotMarketData.Timestamp;
        }
    }

    public class FuturesMarketData : IUpdatable<FuturesMarketData>
    {
        public string Symbol { get; set; } = string.Empty;
        public decimal? LastPrice { get; set; }
        public decimal? MarkPrice { get; set; }
        public decimal? IndexPrice { get; set; }
        public decimal? HighestBid { get; set; }
        public decimal? LowestAsk { get; set; }
        public decimal? OpenInterest { get; set; }
        public decimal? FundingRate { get; set; }
        public DateTime? NextFundingTime { get; set; }
        public decimal? Volume24h { get; set; }
        public decimal? Turnover24h { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public FuturesMarketData()
        {

        }

        // Copy constructor
        public FuturesMarketData(FuturesMarketData futuresData)
        {
            Symbol = futuresData.Symbol;
            LastPrice = futuresData.LastPrice;
            MarkPrice = futuresData.MarkPrice;
            IndexPrice = futuresData.IndexPrice;
            HighestBid = futuresData.HighestBid;
            LowestAsk = futuresData.LowestAsk;
            OpenInterest = futuresData.OpenInterest;
            FundingRate = futuresData.FundingRate;
            NextFundingTime = futuresData.NextFundingTime;
            Volume24h = futuresData.Volume24h;
            Turnover24h = futuresData.Turnover24h;
            Timestamp = futuresData.Timestamp;
        }

        public void Update(FuturesMarketData newData)
        {
            if (!string.IsNullOrEmpty(newData.Symbol))
                Symbol = newData.Symbol;
            
            Timestamp = newData.Timestamp;

            LastPrice = newData.LastPrice ?? LastPrice;
            MarkPrice = newData.MarkPrice ?? MarkPrice;
            IndexPrice = newData.IndexPrice ?? IndexPrice;
            HighestBid = newData.HighestBid ?? HighestBid;
            LowestAsk = newData.LowestAsk ?? LowestAsk;
            OpenInterest = newData.OpenInterest ?? OpenInterest;
            FundingRate = newData.FundingRate ?? FundingRate;
            NextFundingTime = newData.NextFundingTime ?? NextFundingTime;
            Volume24h = newData.Volume24h ?? Volume24h;
            Turnover24h = newData.Turnover24h ?? Turnover24h;
        }
    }

    public class MarketDataRecord
    {
        public SpotMarketData? SpotData { get; set; }
        public FuturesMarketData? FuturesData { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public record AssetBalance
    {
        public string Asset { get; init; } = string.Empty;
        public decimal Total { get; init; }
        public decimal Available { get; init; }
        public decimal InOrder { get; init; }
        public decimal Free { get => Available; init => Available = value; }
        public decimal Locked { get => InOrder; init => InOrder = value; }
    }

    public record WalletUpdate // inspired from BybitBalance.cs->BybitBalance
    {
        public AccountType AccountType { get; set; } = AccountType.Unified;
        public decimal? AccountInitialMarginRate { get; set; }
        public decimal? AccountMaintenanceMarginRate { get; set; }
        public decimal? TotalEquity { get; set; } // Account equity in USD
        public decimal? TotalWalletBalance { get; set; }  // Total wallet balance in USD
        public decimal? TotalMarginBalance { get; set; }  // Total margin balance in USD
        public decimal? TotalAvailableBalance { get; set; }  // Total available balance in USD
        public decimal? TotalPerpUnrealizedPnl { get; set; }  // Unrealized profit and loss in USD
        public decimal? TotalInitialMargin { get; set; }  // Initial margin in USD
        public decimal? TotalMaintenanceMargin { get; set; }  // Maintenance margin in USD
        public IEnumerable<AssetBalance> Assets { get; set; } = Array.Empty<AssetBalance>();
    }

    public record PositionModel
    {
        public Category Category { get; set; }
        public PositionMode PositionMode { get; set; }
        public string Symbol { get; set; } = string.Empty;
        public PositionDirection? Direction { get; set; } // a.k.a. Bybit PositionIdx !
        public PositionSide? Side { get; set; } // a.k.a. Bybit PositionSide !
        public decimal Quantity { get; set; } // Size
        public decimal? AveragePrice { get; set; }
        private decimal? EntryPrice
        {
            get => AveragePrice;
            set => AveragePrice = value;
        }
        public decimal? PositionValue { get; set; }
        public TradeMode TradeMode { get; set; }
        public PositionStatus? PositionStatus { get; set; } = Models.PositionStatus.Normal;
        public decimal? Leverage { get; set; }
        public decimal? MarkPrice { get; set; }
        public decimal? LiquidationPrice { get; set; }
        public decimal? BustPrice { get; set; } // Bankruptcy price
        public decimal? InitialMargin { get; set; }
        public decimal? MaintenanceMargin { get; set; }
        public decimal? TakeProfit { get; set; }
        public decimal? StopLoss { get; set; }
        public decimal? UnrealizedPnl { get; set; }
        public decimal? RealizedPnl { get; set; }
        public decimal? CurrentRealizedPnl { get; set; }
        public DateTime? CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public bool AutoAddMargin { get; set; }
        public decimal? PositionBalance { get; set; }
    }

    public record PositionModelUpdate : PositionModel
    {
    }

    public class OrderModel
    {
        public string OrderId { get; set; } = string.Empty;
        public string? ClientOrderId { get; set; } = null;
        public string Symbol { get; set; } = string.Empty;
        public Category Category { get; set; }
        public decimal? Price { get; set; }
        public decimal Quantity { get; set; }
        public OrderSide Side { get; set; }
        public PositionDirection? PositionDirection { get; set; }
        public bool? IsLeverage { get; set; }
        public OrderStatus Status { get; set; }
        public string? RejectReason { get; set; }
        public decimal? AveragePrice { get; set; }
        public decimal? QuantityRemaining { get; set; }
        public decimal? ValueRemaining { get; set; }
        public decimal? QuantityFilled { get; set; }
        public decimal? ValueFilled { get; set; }
        public Fee ExecutedFee { get; set; } = new Fee(); // Non-nullable with default value
        public TimeInForce TimeInForce { get; set; }
        public OrderType OrderType { get; set; }
        public decimal? TriggerPrice { get; set; }
        //public TriggerType? TriggerType { get; set; }
        public bool IsReduceOnly { get; set; } = false;
    
        public decimal? TakeProfit { get; set; }
        public decimal? StopLoss { get; set; }
        public DateTime UpdateTime { get; set; }
        public decimal? TakeProfitLimitPrice { get; set; }
        public decimal? StopLossLimitPrice { get; set; }
        public DateTime CreateTime { get; set; }
    }

    public class OrderModelUpdate : OrderModel
    {
        // Shadow the base class property with a nullable version
        public new Fee? ExecutedFee { get; set; }
        
        public decimal? ClosedPnl { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Summary of profit and loss information
    /// </summary>
    public class PnLSummary
    {
        /// <summary>
        /// Profit and loss for the current day
        /// </summary>
        public decimal DailyPnL { get; set; }
        
        /// <summary>
        /// Profit and loss for the current week
        /// </summary>
        public decimal WeeklyPnL { get; set; }
        
        /// <summary>
        /// Profit and loss for the current month
        /// </summary>
        public decimal MonthlyPnL { get; set; }
        
        /// <summary>
        /// Total realized profit and loss
        /// </summary>
        public decimal TotalRealizedPnL { get; set; }
        
        /// <summary>
        /// Total unrealized profit and loss
        /// </summary>
        public decimal TotalUnrealizedPnL { get; set; }
    }

    /// <summary>
    /// Represents the configurable settings of a trading account on an exchange.
    /// </summary>
    public class AccountSettings
    {
        /// <summary>
        /// Gets or sets the position mode (One-Way or Hedge).
        /// </summary>
        public PositionMode PositionMode { get; set; } = PositionMode.BothSides;

        /// <summary>
        /// Gets or sets the margin mode (Regular, Portfolio, Isolated).
        /// </summary>
        public MarginMode MarginMode { get; set; } = MarginMode.RegularMargin;

        /// <summary>
        /// Gets or sets the trade mode (Cross or Isolated).
        /// </summary>
        public TradeMode TradeMode { get; set; } = TradeMode.CrossMargin;

        /// <summary>
        /// Gets or sets the leverage used for buying/long positions.
        /// </summary>
        public decimal BuyLeverage { get; set; } = 10m;

        /// <summary>
        /// Gets or sets the leverage used for selling/short positions.
        /// In many cases (like one-way mode or simple leverage settings), this will be the same as BuyLeverage.
        /// </summary>
        public decimal SellLeverage { get; set; } = 10m;
    }
}
﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using MyTraderSpace.Models;
using MyTraderSpace.Utils;
//using MyTraderSpace.Services;
using MyTraderSpace.Logging;

namespace MyTraderSpace.Exchanges.Simulated
{
    /// <summary>
    /// Simulated exchange API implementation that processes orders locally without connecting to a real exchange.
    ///
    /// Order tracking:
    /// - _activeOrders (from BaseExchangeAPI): Tracks all orders that have been placed and are still active on the exchange.
    /// - _pendingOrders: Tracks orders that have been created but are waiting for certain market conditions to be met before execution.
    /// - _completedOrders: Stores a limited history of orders that have been fully executed, cancelled, or rejected.
    /// </summary>
    public class SimulatedExchangeAPI : BaseExchangeAPI
    {
        #region Fields

        private readonly LogManager _log;
        private readonly SimulatedExchangeConfig _simConfig;
        private IDisposable? _spotSubscription;
        private IDisposable? _futuresSubscription;
        private readonly CancellationTokenSource _cts = new();
        private bool _isDisposed = false;
        private readonly object _lockObject = new(); // Lock for order processing, position updates, wallet modifications
        private DateTime _lastSpotUpdateTime = DateTime.MinValue;
        private DateTime _lastFuturesUpdateTime = DateTime.MinValue;

        // Order tracking
        private readonly LimitedConcurrentDictionary<string, OrderModel> _completedOrders = new(1000); // Limit to 1000 completed orders
        private int _orderIdCounter = 1000; // Starting order ID

        // Latest market data for order execution
        private SpotMarketData? _latestSpotData;
        private FuturesMarketData? _latestFuturesData;

        // Direct access to the wallet as SimulatedWallet
        private SimulatedWallet SimulatedWallet => Wallet as SimulatedWallet ?? throw new InvalidOperationException("Wallet is not a SimulatedWallet");

        private decimal? _previousHighestBid = null;
        private decimal? _previousLowestAsk = null;

        private decimal? _previousSpotHighestBid = null;
        private decimal? _previousSpotLowestAsk = null;

        #endregion

        #region Properties

        //public Type ExchangeType => typeof(Models.Simulated);
        public override ExchangeType ExchangeType => ExchangeType.Simulated;


        /// <summary>
        /// Current state of the exchange connection
        /// </summary>
        public override ExchangeState State { get; protected set; } = ExchangeState.Initializing;

        #endregion

        #region Constructor

        /// <summary>
        /// Creates a new instance of the simulated exchange API
        /// </summary>
        /// <param name="tradingPair">The currency pair to trade</param>
        /// <param name="marketDataService">Service providing market data</param>
        /// <param name="config">Configuration for the simulated exchange</param>
        public SimulatedExchangeAPI(
            string name,
            CurrencyPair tradingPair,
            IMarketDataService marketDataService,
            ExchangeConfig? config = null,
            LogLevel? logLevel = null)
            : base(name, tradingPair, marketDataService, new LogManager(name), config)
        {
            _log = base._log;
            _simConfig = config as SimulatedExchangeConfig ?? new SimulatedExchangeConfig();
            _log.SetLogLevel(_simConfig.LogLevel);

            // Create a SimulatedWallet to replace the base Wallet
            // Now we can directly set the Wallet property since it's virtual in the base class
            // This will also set some initial balance values from _simConfig!
            Wallet = new SimulatedWallet(marketDataService, _simConfig);
            _log.Information("Created SimulatedWallet instance");

            // Initialize account settings from config
            _currentPositionMode = _config.PositionMode;
            _currentMarginMode = _config.MarginMode;
            _currentTradeMode = _config.TradeMode;
            _currentBuyLeverage = _config.Leverage;
            _currentSellLeverage = _config.Leverage;

            _log.Information($"Initialized {GetType().Name} for {tradingPair.Symbol}");
            _log.Information($"Default position mode: {_currentPositionMode}");
            _log.Information($"Leverage: {_config.Leverage}x");

            _log.Information($"SimulatedExchangeAPI '{Name}' specific constructor finished.");
        }

        #endregion

        #region Initialization and Shutdown

        /// <summary>
        /// Initializes the exchange connection and subscribes to market data
        /// </summary>
        public override async Task InitializeAsync()
        {
            try
            {
                // Note: ApiCallLatency is not applied to InitializeAsync itself,
                // as it represents the setup of the connection, not a typical API call.
                _log.Information($"Initializing simulated exchange...");

                // Initialize wallet with default balances
                await InitializeWalletAsync();

                // Subscribe to market data
                _log.Information($"Subscribing to market data...");
                SubscribeToMarketData();

                // Wait for initial market data
                var initialData = await WaitForInitialMarketDataAsync();
                if (initialData.Spot != null)
                {
                    _latestSpotData = initialData.Spot;
                }
                if (initialData.Futures != null)
                {
                    _latestFuturesData = initialData.Futures;
                }

                // Set exchange state to ready
                State = ExchangeState.Ready;

                _log.Information($"Simulated exchange initialized successfully");
            }
            catch (Exception ex)
            {
                _log.Error($"Failed to initialize simulated exchange: {ex.Message}");
                State = ExchangeState.Error;
                throw;
            }
        }

        private async Task<(SpotMarketData? Spot, FuturesMarketData? Futures)> WaitForInitialMarketDataAsync()
        {
            // Wait for initial market data with timeout
            var timeout = TimeSpan.FromSeconds(5);
            var startTime = DateTime.UtcNow;

            while (DateTime.UtcNow - startTime < timeout)
            {
                var data = _marketDataService.GetLatestData();
                if (data.Spot != null || data.Futures != null)
                {
                    return data;
                }

                await Task.Delay(100);
            }

            _log.Warning("Timeout waiting for initial market data");
            return (null, null);
        }

        /// <summary>
        /// Initializes the wallet with default balances from configuration
        /// </summary>
        private async Task InitializeWalletAsync()
        {
            try
            {
                // Check if we have a valid wallet
                if (!(Wallet is SimulatedWallet))
                {
                    _log.Error("Simulated wallet not available");
                    return;
                }

                // Initialize the wallet with default balances
                var baseCoin = TradingPair.BaseCoin;
                var quoteCoin = TradingPair.QuoteCoin;

                SimulatedWallet.SetBalance(baseCoin, _simConfig.InitialBaseBalance, 0);
                _log.Information($"Set default base balance: {baseCoin} = {_simConfig.InitialBaseBalance}");

                SimulatedWallet.SetBalance(quoteCoin, _simConfig.InitialQuoteBalance, 0);
                _log.Information($"Set default quote balance: {quoteCoin} = {_simConfig.InitialQuoteBalance}");

                // Create an initial wallet update to notify subscribers
                var walletUpdate = await GetWalletBalancesAsync();
                InvokeWalletUpdate(walletUpdate);
            }
            catch (Exception ex)
            {
                _log.Error($"Failed to initialize wallet: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets the simulated wallet balances. Overrides the base implementation.
        /// </summary>
        /// <returns>A WalletUpdate object reflecting the current simulated state.</returns>
        public override async Task<WalletUpdate> GetWalletBalancesAsync()
        {
            _log.Debug($"Simulating GetWalletBalancesAsync...");
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);

                if (SimulatedWallet == null)
                {
                    _log.Error("SimulatedWallet is null during GetWalletBalancesAsync.");
                    return new WalletUpdate(); // Return empty update
                }

                // Create a WalletUpdate based on the current state of the SimulatedWallet
                var currentBalances = SimulatedWallet.GetAllBalances(); // Gets dictionary<CoinType, AssetBalance>
                var assets = currentBalances.Values.ToList(); // Convert to List<AssetBalance>

                // Calculate total values (example for USDT quote)
                // NOTE: This might need refinement based on how you want to calculate totals in simulation
                decimal totalEquity = 0;
                decimal totalAvailable = 0;
                (var spotData, var futuresData) = _marketDataService.GetLatestData();
                decimal btcPrice = futuresData?.MarkPrice ?? spotData?.LastPrice ?? 0m; // Get a price for conversion

                foreach (var asset in assets)
                {
                    decimal valueInQuote = 0;
                    if (asset.Asset.Equals(TradingPair.QuoteCoin.ToString(), StringComparison.OrdinalIgnoreCase))
                    {
                        valueInQuote = asset.Total;
                    }
                    else if (asset.Asset.Equals(TradingPair.BaseCoin.ToString(), StringComparison.OrdinalIgnoreCase) && btcPrice > 0)
                    {
                        valueInQuote = asset.Total * btcPrice;
                    }
                    // Add conversions for other assets if needed
                    totalEquity += valueInQuote;
                    // Simplification: Assume all total balance is available for now in sim
                    totalAvailable += valueInQuote;
                }

                var walletUpdate = new WalletUpdate
                {
                    AccountType = AccountType.Unified, // Or appropriate type
                    TotalEquity = totalEquity,
                    TotalWalletBalance = totalEquity, // Simplification
                    TotalAvailableBalance = totalAvailable, // Simplification
                    TotalPerpUnrealizedPnl = GetUnrealizedPnl(), // Get from base class tracking
                    Assets = assets
                    // Populate other fields like margin if necessary for simulation logic
                };

                // IMPORTANT: Update the internal cache and notify listeners
                InvokeWalletUpdate(walletUpdate);

                _log.Debug($"Simulated GetWalletBalancesAsync complete. Equity: {walletUpdate.TotalEquity:F2}");
                return walletUpdate;
            }
            catch (Exception ex)
            {
                _log.Error($"Error simulating GetWalletBalancesAsync: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Subscribes to market data feeds
        /// </summary>
        private void SubscribeToMarketData()
        {
            _log.Information("Subscribing to market data...");

            if (_marketDataService.SupportsSpot)
            {
                _log.Information($"Subscribing to spot market data for {TradingPair.Symbol}");
                _spotSubscription = _marketDataService.SubscribeToSpotMarketData(TradingPair, OnSpotMarketDataUpdate);
            }

            if (_marketDataService.SupportsFutures)
            {
                _log.Information($"Subscribing to futures market data for {TradingPair.Symbol}");
                _futuresSubscription = _marketDataService.SubscribeToFuturesMarketData(TradingPair, OnFuturesMarketDataUpdate);
            }
        }

        /// <summary>
        /// Handles spot market data updates
        /// </summary>
        private void OnSpotMarketDataUpdate(SpotMarketData data)
        {
            if (data == null)
                return;

            // Process market update with rate limiting if configured
            lock (_lockObject)
            {
                if (_simConfig.MinIntervalBetweenMarketTickProcessing.TotalMilliseconds > 0)
                {
                    var elapsed = (DateTime.UtcNow - _lastSpotUpdateTime).TotalMilliseconds;
                    if (elapsed < _simConfig.MinIntervalBetweenMarketTickProcessing.TotalMilliseconds)
                        return;

                    _lastSpotUpdateTime = DateTime.UtcNow;
                }

                // --- Store previous SPOT tick data BEFORE updating _latestSpotData ---
                if (_latestSpotData != null)
                {
                    _previousSpotHighestBid = _latestSpotData.HighestBid;
                    _previousSpotLowestAsk = _latestSpotData.LowestAsk;
                    _log.Debug($"Stored previous SPOT tick: PrevBid={_previousSpotHighestBid}, PrevAsk={_previousSpotLowestAsk}");
                }
                // Store the latest market data for order execution
                _latestSpotData = data;

                // Only process orders if the exchange is ready
                if (State == ExchangeState.Ready)
                {
                    // Process limit orders if needed
                    try
                    {
                        // Process any pending limit orders that can now be executed
                        ProcessPendingSpotOrders(data);

                        // Invoke update for subscribers
                        InvokeSpotUpdate(data);
                    }
                    catch (Exception ex)
                    {
                        _log.Error($"Error processing spot market update: {ex.Message}");
                    }
                }
                else
                {
                    // Just invoke the update for subscribers
                    InvokeSpotUpdate(data);
                }
            }
        }

        /// <summary>
        /// Process any pending spot orders that can now be executed based on the current market data
        /// </summary>
        private void ProcessPendingSpotOrders(SpotMarketData data)
        {
            // Get all active spot limit orders for this symbol that are New
            var activeLimitOrders = _activeOrders.Values
                .Where(o => o.Symbol == data.Symbol &&
                            o.Category == Category.Spot &&
                            o.Status == OrderStatus.New &&
                            o.OrderType == OrderType.Limit && // Ensure it's a limit order
                            o.Price.HasValue)
                .ToList(); // ToList to avoid modifying collection while iterating

            foreach (var order in activeLimitOrders)
            {
                // Check if the order can be executed based on market data
                bool canExecute = false;
                decimal executionPrice = order.Price!.Value; // Limit orders execute at their price

                // --- Refined Fill Logic for Spot Orders ---
                if (order.Side == OrderSide.Buy) // Buy Limit Order
                {
                    decimal currentAsk = data.LowestAsk; // Corrected: Directly assign non-nullable value
                    // Use current ask if no previous ask available
                    decimal effectivePreviousAsk = _previousSpotLowestAsk ?? currentAsk;

                    // Fill if the lowest point reached (between prev and current tick) went at or below the limit price
                    if (Math.Min(currentAsk, effectivePreviousAsk) <= executionPrice)
                    {
                        _log.Debug($"Spot Buy Limit Order {order.ClientOrderId ?? order.OrderId} ({order.Side} @ {executionPrice}) fill condition met: Math.Min(CurrentAsk={currentAsk}, PrevAsk={effectivePreviousAsk}) <= LimitPrice={executionPrice}");
                        canExecute = true;
                    }
                }
                else if (order.Side == OrderSide.Sell) // Sell Limit Order
                {
                    decimal currentBid = data.HighestBid; // Corrected: Directly assign non-nullable value
                    // Use current bid if no previous bid available
                    decimal effectivePreviousBid = _previousSpotHighestBid ?? currentBid;

                    // Fill if the highest point reached (between prev and current tick) went at or above the limit price
                    if (Math.Max(currentBid, effectivePreviousBid) >= executionPrice)
                    {
                        _log.Debug($"Spot Sell Limit Order {order.ClientOrderId ?? order.OrderId} ({order.Side} @ {executionPrice}) fill condition met: Math.Max(CurrentBid={currentBid}, PrevBid={effectivePreviousBid}) >= LimitPrice={executionPrice}");
                        canExecute = true;
                    }
                }
                // --- End Refined Fill Logic ---

                if (canExecute)
                {
                    try
                    {
                        // Use a lock to ensure thread safety during order execution
                        lock (_lockObject)
                        {
                            // Double-check if the order still exists in _activeOrders and is still New
                            // (it might have been cancelled or filled by another thread between the query and now)
                            if (!_activeOrders.TryGetValue(order.OrderId, out var currentOrder) ||
                                currentOrder.Status != OrderStatus.New)
                            {
                                // Order was already processed or cancelled
                                continue; // Skip to the next order
                            }

                            // Execute the order using its existing ID
                            _log.Information($"Attempting to execute triggered limit order {order.OrderId}");
                            var result = ExecuteSpotOrder(
                                currentOrder.Symbol,
                                currentOrder.Side,
                                currentOrder.Quantity,
                                executionPrice, // Execute at the limit price
                                currentOrder.OrderType,
                                currentOrder.OrderId,
                                currentOrder.ClientOrderId); // Pass the existing OrderId

                            // ExecuteSpotOrder now handles updating status, calling HandleOrderUpdate (which removes from _activeOrders),
                            // and adding to _completedOrders.

                            if (result.IsSuccess)
                            {
                                 _log.Information($"Successfully executed triggered limit order {order.OrderId}: {order.Side} {order.Quantity} {order.Symbol} @ {executionPrice}");
                            }
                            else
                            {
                                 _log.Warning($"Failed to execute triggered limit order {order.OrderId}: {result.Message}");
                                 // Should we change order status to Rejected here? Or let ExecuteSpotOrder handle it?
                                 // For now, assume ExecuteSpotOrder handles the final state transition.
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex, $"Error executing pending spot order {order.OrderId}");
                        // Optionally, mark the order as Rejected in _activeOrders here if execution fails critically
                        // MarkOrderAsRejected(order.OrderId, $"Execution exception: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// Handles futures market data updates
        /// </summary>
        private void OnFuturesMarketDataUpdate(FuturesMarketData data)
        {
            if (data == null || State != ExchangeState.Ready)
                return;

            // Process market update with rate limiting if configured
            lock (_lockObject)
            {
                if (_simConfig.MinIntervalBetweenMarketTickProcessing.TotalMilliseconds > 0)
                {
                    var elapsed = (DateTime.UtcNow - _lastFuturesUpdateTime).TotalMilliseconds;
                    if (elapsed < _simConfig.MinIntervalBetweenMarketTickProcessing.TotalMilliseconds)
                        return;

                    _lastFuturesUpdateTime = DateTime.UtcNow;
                }

                // --- Store previous tick data BEFORE updating _latestFuturesData ---
                if (_latestFuturesData != null)
                {
                    _previousHighestBid = _latestFuturesData.HighestBid;
                    _previousLowestAsk = _latestFuturesData.LowestAsk;
                    _log.Debug($"Stored previous tick: PrevBid={_previousHighestBid}, PrevAsk={_previousLowestAsk}");
                }
                // Update latest data
                _latestFuturesData = data;

                try
                {
                    // --- Update Unrealized PnL, Maintenance Margin, and Liquidation Price for Open Positions ---
                    if (data.MarkPrice.HasValue) // Ensure we have a valid mark price
                    {
                        decimal currentMarkPrice = data.MarkPrice.Value;
                        // Iterate through a copy of the values to avoid collection modification issues if needed later
                        var positionsToUpdate = _openPositions.Values.ToList();

                        foreach (var position in positionsToUpdate)
                        {
                            // Only update Linear positions that are actually open
                            if (position.Category == Category.Linear && position.Quantity > 0 && position.AveragePrice.HasValue && position.Leverage.HasValue)
                            {
                                decimal entryPrice = position.AveragePrice.Value;
                                decimal leverage = position.Leverage.Value;

                                // Update Mark Price on the position model
                                position.MarkPrice = currentMarkPrice;

                                // Calculate and Update Unrealized PnL
                                decimal upl = 0;
                                if (position.Side == PositionSide.Buy) // Long position
                                {
                                    upl = (currentMarkPrice - entryPrice) * position.Quantity;
                                }
                                else if (position.Side == PositionSide.Sell) // Short position
                                {
                                    upl = (entryPrice - currentMarkPrice) * position.Quantity;
                                }

                                // Update the position object directly
                                // Note: If multiple threads accessed _openPositions heavily,
                                // we might need a ConcurrentDictionary update mechanism, but
                                // for now, direct update within the lock should be safe.
                                position.UnrealizedPnl = upl;

                                // Calculate and Update Maintenance Margin
                                decimal positionValue = Math.Abs(position.Quantity) * currentMarkPrice;
                                decimal maintenanceMargin = positionValue * _simConfig.MaintenanceMarginRate;
                                position.MaintenanceMargin = maintenanceMargin;

                                // Calculate and Update Liquidation Price (Simplified Isolated Formula)
                                decimal liqPrice = 0;
                                if (leverage > 0) // Avoid division by zero if leverage is somehow invalid
                                {
                                    if (position.Side == PositionSide.Buy) // Long
                                    {
                                        // Liq. Price = Entry * (1 - 1 / Leverage)
                                        liqPrice = entryPrice * (1 - (1 / leverage));
                                    }
                                    else // Short
                                    {
                                        // Liq. Price = Entry * (1 + 1 / Leverage)
                                        liqPrice = entryPrice * (1 + (1 / leverage));
                                    }
                                    // Ensure liquidation price isn't negative
                                    position.LiquidationPrice = Math.Max(0, liqPrice);
                                }
                                else
                                {
                                    position.LiquidationPrice = null; // Cannot calculate if leverage is invalid
                                }
                                // _log.Debug($"Updated {position.Symbol} {position.Direction}: UPL={upl:F4}, MM={maintenanceMargin:F4}, LiqP={position.LiquidationPrice ?? -1}"); // Optional detailed logging

                                // TODO: Decide if we need to fire OnPositionUpdate here.
                                // Firing it on every tick could be very noisy.
                                // Strategies/UI might need to poll GetUnrealizedPnl() or GetPositionsAsync() instead.
                            }
                        }
                    }
                    // --- End Update Unrealized PnL, Maintenance Margin, and Liquidation Price ---

                    // Process limit orders if needed using the NEW data and the stored PREVIOUS data
                    ProcessPendingFuturesOrders(data); // This method will now use _previousHighestBid/_previousLowestAsk

                    // Invoke update for external subscribers (strategies, UI, etc.)
                    InvokeFuturesUpdate(data);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, "Error processing futures market update"); // Added Exception details
                }
            }
        }

        /// <summary>
        /// Process any pending futures orders that can now be executed based on the current market data
        /// </summary>
        private void ProcessPendingFuturesOrders(FuturesMarketData data) // Parameter 'data' is the CURRENT tick
        {
            // Get all active Linear limit orders for this symbol that are New
             var activeLimitOrders = _activeOrders.Values
                .Where(o => o.Symbol == data.Symbol &&
                            o.Category == Category.Linear &&
                            o.Status == OrderStatus.New &&
                            o.OrderType == OrderType.Limit && // Ensure it's a limit order
                            o.Price.HasValue)
                .ToList(); // ToList to avoid modifying collection while iterating

            //if (activeLimitOrders.Count > 0)
            //{
            //    _log.Debug($"Checking {activeLimitOrders.Count} active limit orders against tick @ {data.Timestamp:O} (Bid={data.HighestBid}, Ask={data.LowestAsk}) | Prev (Bid={_previousHighestBid}, Ask={_previousLowestAsk})");
            //}


            foreach (var order in activeLimitOrders)
            {
                // Check if the order can be executed based on market data
                bool canExecute = false;
                decimal executionPrice = order.Price!.Value; // **Limit orders execute at their price**

                // --- Refined Fill Logic ---
                if (order.Side == OrderSide.Buy) // Buy Limit Order
                {
                    decimal currentAsk = data.LowestAsk ?? decimal.MaxValue;
                    // Use current ask if no previous ask available (e.g., first check after order placement)
                    decimal effectivePreviousAsk = _previousLowestAsk ?? currentAsk;

                    // Fill if the lowest point reached (between prev and current tick) went at or below the limit price
                    if (Math.Min(currentAsk, effectivePreviousAsk) <= executionPrice)
                    {
                        _log.Debug($"Buy Limit Order {order.ClientOrderId ?? order.OrderId} ({order.Side} @ {executionPrice}) fill condition met: Math.Min(CurrentAsk={currentAsk}, PrevAsk={effectivePreviousAsk}) <= LimitPrice={executionPrice}");
                        canExecute = true;
                    }
                }
                else // Sell Limit Order
                {
                    decimal currentBid = data.HighestBid ?? decimal.MinValue;
                    // Use current bid if no previous bid available
                    decimal effectivePreviousBid = _previousHighestBid ?? currentBid;

                    // Fill if the highest point reached (between prev and current tick) went at or above the limit price
                    if (Math.Max(currentBid, effectivePreviousBid) >= executionPrice)
                    {
                        _log.Debug($"Sell Limit Order {order.ClientOrderId ?? order.OrderId} ({order.Side} @ {executionPrice}) fill condition met: Math.Max(CurrentBid={currentBid}, PrevBid={effectivePreviousBid}) >= LimitPrice={executionPrice}");
                        canExecute = true;
                    }
                }
                // --- End Refined Fill Logic ---

                if (canExecute)
                {
                    try
                    {
                        // Use a lock to ensure thread safety during order execution
                        lock (_lockObject)
                        {
                             // Double-check if the order still exists in _activeOrders and is still New
                            if (!_activeOrders.TryGetValue(order.OrderId, out var currentOrder) ||
                                currentOrder.Status != OrderStatus.New)
                            {
                                // Order was already processed or cancelled
                                continue; // Skip to the next order
                            }

                            // Get the leverage from the current settings
                            // Note: Leverage should ideally be stored with the order when placed,
                            // but for now, we retrieve current leverage.
                            decimal leverage = currentOrder.Side == OrderSide.Buy ? _currentBuyLeverage : _currentSellLeverage;

                            // CRITICAL FIX: Use the PositionDirection stored on the order for Hedge Mode.
                            PositionDirection? effectivePositionDirection = currentOrder.PositionDirection;

                            _log.Information($"Attempting to execute triggered limit order {currentOrder.OrderId} (Side: {currentOrder.Side}, ReduceOnly: {currentOrder.IsReduceOnly}, EffectivePosDir: {effectivePositionDirection})");

                            // Execute the order using its existing ID
                            var result = ExecuteFuturesOrder(
                                currentOrder.Symbol,
                                currentOrder.Side,
                                currentOrder.Quantity,
                                executionPrice, // Execute at the limit price
                                leverage,
                                currentOrder.OrderType,
                                effectivePositionDirection, // Pass the correct PositionDirection from the order
                                currentOrder.OrderId,  // Pass the existing OrderId
                                currentOrder.IsReduceOnly, // Pass the ReduceOnly flag from the order
                                currentOrder.ClientOrderId);

                             // ExecuteFuturesOrder handles the rest (status update, HandleOrderUpdate, _completedOrders)

                            if (result.IsSuccess)
                            {
                                _log.Information($"Successfully executed triggered limit order {order.OrderId}: {order.Side} {order.Quantity} {order.Symbol} @ {executionPrice}");
                            }
                            else
                            {
                                 _log.Warning($"Failed to execute triggered limit order {order.OrderId}: {result.Message}");
                                 // MarkOrderAsRejected(order.OrderId, result.Message); // Consider adding this helper
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex, $"Error executing pending futures order {order.OrderId}");
                         // MarkOrderAsRejected(order.OrderId, $"Execution exception: {ex.Message}"); // Consider adding this helper
                    }
                }
            }
        }

        /// <summary>
        /// Executes a futures order and updates positions and wallet
        /// </summary>
        /// <returns>The executed order details</returns>
        private FuturesOrderResult ExecuteFuturesOrder(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal executionPrice, // Renamed from price to reflect its role
            decimal leverage,
            OrderType orderType,
            PositionDirection? positionDirection,
            string? orderId = null, // Make orderId nullable
            bool reduceOnly = false,
            string? clientId = null)
        {
             bool isExistingOrder = !string.IsNullOrEmpty(orderId);
             string orderIdToUse = isExistingOrder ? orderId! : GenerateOrderId();
             OrderModel? existingOrderModel = null;
             DateTime createTime = DateTime.UtcNow; // Default for new orders
             decimal finalExecutionPrice = executionPrice; // Use this for the final price after potential slippage

            try // Outer catch block will handle ArgumentException from parsing now
            {
                // Validate leverage against config's MaxLeverage
                if (leverage < 1 || leverage > _simConfig.MaxLeverage)
                {
                    _log.Error($"Invalid leverage: {leverage}. Must be between 1 and {_simConfig.MaxLeverage}");
                    // MarkOrderAsRejected(orderIdToUse, $"Invalid leverage: {leverage}");
                    return CreateFuturesErrorResult($"Invalid leverage: {leverage}. Must be between 1 and {_simConfig.MaxLeverage}");
                }

                // If it's an existing order (limit order being triggered), retrieve its model
                if (isExistingOrder)
                {
                    if (_activeOrders.TryGetValue(orderIdToUse, out existingOrderModel))
                    {
                         createTime = existingOrderModel.CreateTime; // Use original creation time
                         _log.Debug($"Executing existing futures order {orderIdToUse}.");
                         // Inherit reduceOnly flag from the existing order model
                         reduceOnly = existingOrderModel.IsReduceOnly;
                         // Limit orders execute at their specific price, no slippage applied here
                         finalExecutionPrice = existingOrderModel.Price ?? executionPrice; // Use order's price if available
                    }
                    else
                    {
                        _log.Error($"Cannot execute futures order. Order ID {orderIdToUse} provided but not found in active orders.");
                        return CreateFuturesErrorResult($"Order ID {orderIdToUse} not found in active orders.");
                    }
                }
                else // This is a new Market or immediately executable Limit order
                {
                    _log.Debug($"Executing new futures order {orderIdToUse}.");

                    // --- Apply Slippage for NEW Market Orders ONLY if enabled ---
                    if (_simConfig.SimulateSlippage && orderType == OrderType.Market && _latestFuturesData != null)
                    {
                        decimal basePrice;
                        // Prefer Bid/Ask for slippage base if available
                        if (side == OrderSide.Buy && _latestFuturesData.LowestAsk > 0)
                        {
                            basePrice = _latestFuturesData.LowestAsk.Value; // Use nullable LowestAsk directly
                        }
                        else if (side == OrderSide.Sell && _latestFuturesData.HighestBid > 0)
                        {
                            basePrice = _latestFuturesData.HighestBid.Value; // Use nullable HighestBid directly
                        }
                        else // Fallback to MarkPrice
                        {
                            // Use initial executionPrice (which was MarkPrice for market orders) if MarkPrice is null
                            basePrice = _latestFuturesData.MarkPrice ?? executionPrice;
                        }

                        if (side == OrderSide.Buy)
                        {
                            finalExecutionPrice = basePrice * (1 + _simConfig.MarketOrderSlippage);
                            _log.Debug($"Applied Market Buy Slippage: Base={basePrice}, Slippage={_simConfig.MarketOrderSlippage * 100}%, Final={finalExecutionPrice}");
                        }
                        else // Sell
                        {
                            finalExecutionPrice = basePrice * (1 - _simConfig.MarketOrderSlippage);
                            _log.Debug($"Applied Market Sell Slippage: Base={basePrice}, Slippage={_simConfig.MarketOrderSlippage * 100}%, Final={finalExecutionPrice}");
                        }
                    }
                    // If SimulateSlippage is false, finalExecutionPrice remains the initially determined price (MarkPrice for Market orders)
                    // --- End Slippage ---

                    // --- ReduceOnly Check (for NEW orders) ---
                    // If placing a new market/immediate limit order with reduceOnly, check constraint now.
                    if (reduceOnly)
                    {
                        if (!CanReducePosition(symbol, side, positionDirection, quantity))
                        {
                            _log.Warning($"Rejecting new ReduceOnly order {orderIdToUse}: No matching position to reduce or insufficient size.");
                            return CreateFuturesErrorResult("Order rejected: ReduceOnly constraint failed (no position/size).");
                        }
                    }
                     // --- End ReduceOnly Check (for NEW orders) ---
                }

                // --- ReduceOnly Check (for existing orders being triggered) ---
                // Re-check here in case the position closed/changed between placing and triggering
                if (isExistingOrder && reduceOnly) // Use the effective reduceOnly value
                {
                    if (!CanReducePosition(symbol, side, positionDirection, quantity))
                    {
                        _log.Warning($"Rejecting triggered ReduceOnly order {orderIdToUse}: Position closed or changed before execution.");
                        // TODO: Consider cancelling the order in _activeOrders instead of just rejecting execution?
                        // For now, reject the execution.
                        return CreateFuturesErrorResult("Order rejected: ReduceOnly constraint failed (position closed/changed).");
                    }
                }
                // --- End ReduceOnly Check ---


                // --- Margin and Wallet Check ---
                // Use finalExecutionPrice for margin calculation
                decimal requiredMargin = (quantity * finalExecutionPrice) / leverage;
                // Removed inline try-catch for parsing
                CurrencyPair parsedPair = CommonPairs.ParseSymbolToCurrencyPair(symbol);
                CoinType quoteCoin = parsedPair.QuoteCoin; // Get QuoteCoin from parsed pair

                if (SimulatedWallet == null)
                {
                    _log.Error($"Simulated wallet not available for order {orderIdToUse}");
                    // MarkOrderAsRejected(orderIdToUse, "Wallet unavailable");
                    return CreateFuturesErrorResult("Simulated wallet not available");
                }

                // Check/Deduct Margin
                if (isExistingOrder) // Limit order triggered
                {
                    // Funds should have been locked; here we "consume" them conceptually.
                    // We previously deducted from balance, now we just verify margin was handled.
                    // The actual margin release/adjustment happens on position close/update.
                    // We might need a 'LockedMargin' concept in SimulatedWallet eventually.
                     _log.Debug($"Consuming pre-locked margin for order {orderIdToUse}");
                }
                else // New market/immediate limit order
                {
                    // Deduct margin directly from available balance
                    if (!SimulatedWallet.DeductBalance(quoteCoin, requiredMargin))
                    {
                        _log.Warning($"Insufficient {quoteCoin} balance for margin. Required: {requiredMargin}, Available: {SimulatedWallet.GetBalance(quoteCoin)}");
                        // MarkOrderAsRejected(orderIdToUse, $"Insufficient {quoteCoin} for margin");
                        return CreateFuturesErrorResult($"Insufficient {quoteCoin} balance for margin requirement");
                    }
                     _log.Debug($"Locked margin {requiredMargin} {quoteCoin} for new order {orderIdToUse}");
                }
                // --- End Margin and Wallet Check ---


                // --- Order Model Update ---
                var filledOrder = existingOrderModel ?? new OrderModel { OrderId = orderIdToUse, ClientOrderId = clientId, CreateTime = createTime };
                filledOrder.Symbol = symbol;
                filledOrder.Side = side;
                filledOrder.Quantity = quantity;
                filledOrder.Price = finalExecutionPrice; // Store the actual execution price
                filledOrder.OrderType = orderType;
                filledOrder.Status = OrderStatus.Filled; // Final state
                filledOrder.Category = Category.Linear;
                filledOrder.UpdateTime = DateTime.UtcNow;
                filledOrder.AveragePrice = finalExecutionPrice; // Average price is the execution price for full fill
                filledOrder.QuantityFilled = quantity;
                filledOrder.ValueFilled = quantity * finalExecutionPrice; // Use final price
                filledOrder.QuantityRemaining = 0;
                filledOrder.ValueRemaining = 0;
                filledOrder.IsLeverage = true; // Mark as leverage order

                 // Calculate and track fees
                bool isMaker = isExistingOrder && orderType == OrderType.Limit;
                // Use finalExecutionPrice for fee calculation
                decimal feeAmount = CalculateFee(filledOrder.ValueFilled.Value, isMaker, false); // Futures fee
                CurrencyPair tradingPair = CommonPairs.ParseSymbolToCurrencyPair(symbol); // Reparse needed here? Could pass parsedPair
                TrackFee(tradingPair.QuoteCoin, feeAmount); // Track fee
                filledOrder.ExecutedFee = new Fee(
                    new CurrencyAmount(tradingPair.BaseCoin, 0),
                    new CurrencyAmount(tradingPair.QuoteCoin, feeAmount)
                );
                // --- End Order Model Update ---

                // Add to completed orders history
                _completedOrders[orderIdToUse] = filledOrder;

                // Update position BEFORE triggering order update, so listeners see consistent state
                // Pass finalExecutionPrice to position update
                UpdateFuturesPosition(symbol, side, quantity, finalExecutionPrice, leverage, positionDirection, orderIdToUse);

                // Create an order update to trigger events & update tracking in base class
                var orderUpdate = CreateOrderUpdateFromModel(filledOrder); // Use helper

                // Update order tracking (HandleOrderUpdate removes from _activeOrders)
                HandleOrderUpdate(orderUpdate);

                // Create and return the result
                return new FuturesOrderResult
                {
                    IsSuccess = true,
                    OrderId = orderIdToUse,
                    ExecutedPrice = finalExecutionPrice, // Return actual execution price
                    ExecutedQuantity = quantity,
                    Status = OrderStatus.Filled,
                    Timestamp = filledOrder.UpdateTime,
                    Message = "Order executed successfully",
                    Leverage = leverage
                };
            }
            catch (Exception ex) // Outer catch block handles all exceptions now, including ArgumentException from parsing
            {
                _log.Error(ex, $"Error executing futures order {orderIdToUse}: {ex.Message}"); // Added OrderId to log
                return CreateFuturesErrorResult($"Error executing futures order: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates or creates a futures position based on the executed order
        /// </summary>
        private void UpdateFuturesPosition(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal executionPrice,
            decimal leverage,
            PositionDirection? positionDirection,
            string orderId)
        {
            // Determine the position key based on position mode
            string positionKey = GetPositionKey(symbol, positionDirection); // Use base class helper

            // Check if we already have a position for this symbol in the base collection
            // Ensure we only consider Linear positions here
            if (_openPositions.TryGetValue(positionKey, out var existingPosition) && existingPosition.Category == Category.Linear)
            {
                // Update existing position
                UpdateExistingPosition(existingPosition, side, quantity, executionPrice, leverage, orderId);
            }
            else
            {
                // Create new position
                CreateNewPosition(positionKey, symbol, side, quantity, executionPrice, leverage, positionDirection, orderId);
            }
        }

        /// <summary>
        /// Updates an existing Linear futures position stored in _openPositions
        /// </summary>
        private void UpdateExistingPosition(
            PositionModel existingPosition,
            OrderSide side,
            decimal quantity,
            decimal executionPrice,
            decimal leverage,
            string orderId)
        {
            var now = DateTime.UtcNow;

            // Convert OrderSide to PositionSide for comparison
            PositionSide positionSide = side == OrderSide.Buy ? PositionSide.Buy : PositionSide.Sell;

            // Determine if this order increases or decreases the position
            bool isIncreasingPosition = (existingPosition.Side == positionSide);

            if (isIncreasingPosition)
            {
                // Increasing the position size
                decimal newSize = existingPosition.Quantity + quantity;
                decimal newValue = (existingPosition.PositionValue ?? 0) + (quantity * executionPrice);

                // Calculate new average entry price
                decimal newEntryPrice = newValue / newSize;

                // Update the position
                existingPosition.Quantity = newSize;
                existingPosition.PositionValue = newValue;
                existingPosition.AveragePrice = newEntryPrice;
                existingPosition.Leverage = leverage;
                existingPosition.UpdateTime = now;
            }
            else
            {
                // Decreasing or potentially closing/flipping the position
                if (quantity < existingPosition.Quantity)
                {
                    // Partial close - reduce position size
                    decimal closingRatio = quantity / existingPosition.Quantity;
                    decimal closingValue = (existingPosition.PositionValue ?? 0) * closingRatio;

                    existingPosition.Quantity -= quantity;
                    existingPosition.PositionValue -= closingValue;
                    existingPosition.UpdateTime = now;

                    // Calculate realized PNL
                    decimal realizedPnl = CalculateRealizedPnl(
                        existingPosition.Side == PositionSide.Buy ? OrderSide.Buy : OrderSide.Sell,
                        quantity,
                        existingPosition.AveragePrice ?? executionPrice,
                        executionPrice);

                    // Update wallet with realized PnL
                    CurrencyPair tradingPair = CommonPairs.ParseSymbolToCurrencyPair(existingPosition.Symbol);
                    if (realizedPnl > 0 && SimulatedWallet != null)
                    {
                        SimulatedWallet.AddBalance(tradingPair.QuoteCoin, realizedPnl);
                    }
                    else if (realizedPnl < 0 && SimulatedWallet != null)
                    {
                        SimulatedWallet.DeductBalance(tradingPair.QuoteCoin, Math.Abs(realizedPnl));
                    }

                    // Update realized PnL in the position
                    existingPosition.RealizedPnl = (existingPosition.RealizedPnl ?? 0) + realizedPnl;

                    // Calculate and track fees
                    bool isSpot = existingPosition.Category == Category.Spot;
                    decimal fee = CalculateFee(quantity * executionPrice, false, isSpot);
                    TrackFee(tradingPair.QuoteCoin, fee);
                }
                else if (quantity == existingPosition.Quantity)
                {
                    // Full close - remove position
                    // Calculate realized PnL
                    decimal realizedPnl = CalculateRealizedPnl(
                        existingPosition.Side == PositionSide.Buy ? OrderSide.Buy : OrderSide.Sell,
                        existingPosition.Quantity,
                        existingPosition.AveragePrice ?? executionPrice,
                        executionPrice);

                    // Update wallet with realized PnL and return margin
                    CurrencyPair tradingPair = CommonPairs.ParseSymbolToCurrencyPair(existingPosition.Symbol);
                    decimal marginToReturn = (existingPosition.PositionValue ?? 0) / (existingPosition.Leverage ?? 1);

                    if (SimulatedWallet != null)
                    {
                        // Return the margin
                        SimulatedWallet.AddBalance(tradingPair.QuoteCoin, marginToReturn);

                        // Add/subtract realized PnL
                        if (realizedPnl > 0)
                        {
                            SimulatedWallet.AddBalance(tradingPair.QuoteCoin, realizedPnl);
                        }
                        else if (realizedPnl < 0)
                        {
                            SimulatedWallet.DeductBalance(tradingPair.QuoteCoin, Math.Abs(realizedPnl));
                        }
                    }

                    // Calculate and track fees
                    decimal fee = CalculateFee(existingPosition.Quantity * executionPrice, false, existingPosition.Category == Category.Spot);
                    TrackFee(tradingPair.QuoteCoin, fee);

                    // Remove the position
                    string positionKey = GetPositionKey(existingPosition.Symbol, existingPosition.Direction);

                    // Use the base _openPositions dictionary
                    if (_openPositions.TryRemove(positionKey, out _))
                    {
                        _log.Information($"Closed position for {existingPosition.Symbol} {existingPosition.Direction} (Removed from _openPositions)");
                    }
                    else
                    {
                        _log.Warning($"Failed to remove position for {existingPosition.Symbol} {existingPosition.Direction}");
                    }

                    // Publish position closed update
                    var positionUpdate = new PositionModelUpdate
                    {
                        Symbol = existingPosition.Symbol,
                        Side = existingPosition.Side,
                        Quantity = 0,
                        PositionValue = 0,
                        AveragePrice = 0,
                        MarkPrice = executionPrice,
                        Leverage = existingPosition.Leverage,
                        PositionStatus = PositionStatus.Normal,
                        Direction = existingPosition.Direction,
                        UpdateTime = now,
                        RealizedPnl = realizedPnl
                    };

                    // Update position tracking and trigger events
                    HandlePositionUpdate(positionUpdate);

                    // Exit early since we've already handled the position update
                    return;
                }
                else
                {
                    // Position flip - close existing and open new in opposite direction
                    // First, close the existing position completely
                    decimal realizedPnl = CalculateRealizedPnl(
                        existingPosition.Side == PositionSide.Buy ? OrderSide.Buy : OrderSide.Sell,
                        existingPosition.Quantity,
                        existingPosition.AveragePrice ?? executionPrice,
                        executionPrice);

                    // Update wallet with realized PnL and return margin
                    CurrencyPair tradingPair = CommonPairs.ParseSymbolToCurrencyPair(existingPosition.Symbol);
                    decimal marginToReturn = (existingPosition.PositionValue ?? 0) / (existingPosition.Leverage ?? 1);

                    if (SimulatedWallet != null)
                    {
                        // Return the margin
                        SimulatedWallet.AddBalance(tradingPair.QuoteCoin, marginToReturn);

                        // Add/subtract realized PNL
                        if (realizedPnl > 0)
                        {
                            SimulatedWallet.AddBalance(tradingPair.QuoteCoin, realizedPnl);
                        }
                        else if (realizedPnl < 0)
                        {
                            SimulatedWallet.DeductBalance(tradingPair.QuoteCoin, Math.Abs(realizedPnl));
                        }
                    }

                    // Calculate and track fees
                    decimal fee = CalculateFee(existingPosition.Quantity * executionPrice, false, existingPosition.Category == Category.Spot);
                    TrackFee(tradingPair.QuoteCoin, fee);

                    // Calculate the new position size (remaining after closing the existing position)
                    decimal newSize = quantity - existingPosition.Quantity;

                    // Update the position with the new direction and size
                    existingPosition.Side = side == OrderSide.Buy ? PositionSide.Buy : PositionSide.Sell;
                    existingPosition.Direction = side == OrderSide.Buy ? PositionDirection.Buy : PositionDirection.Sell;
                    existingPosition.Quantity = newSize;
                    existingPosition.PositionValue = newSize * executionPrice;
                    existingPosition.AveragePrice = executionPrice;
                    existingPosition.Leverage = leverage;
                    existingPosition.UpdateTime = now;
                    existingPosition.RealizedPnl = (existingPosition.RealizedPnl ?? 0) + realizedPnl;
                }
            }

            // Publish position update
            var update = new PositionModelUpdate
            {
                Symbol = existingPosition.Symbol,
                Side = existingPosition.Side,
                Quantity = existingPosition.Quantity,
                PositionValue = existingPosition.PositionValue,
                AveragePrice = existingPosition.AveragePrice,
                MarkPrice = executionPrice,
                Leverage = existingPosition.Leverage,
                PositionStatus = PositionStatus.Normal,
                Direction = existingPosition.Direction,
                UpdateTime = now,
                RealizedPnl = existingPosition.RealizedPnl
            };

            // Update position tracking and trigger events
            HandlePositionUpdate(update);
        }

        /// <summary>
        /// Creates a new Linear futures position in _openPositions
        /// </summary>
        private void CreateNewPosition(
            string positionKey,
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal executionPrice,
            decimal leverage,
            PositionDirection? positionDirection,
            string orderId)
        {
            var now = DateTime.UtcNow;

            // Determine position direction based on order side
            var direction = positionDirection ??
                (side == OrderSide.Buy ? PositionDirection.Buy : PositionDirection.Sell);

            // Always set the Side property based on the order side, regardless of position mode
            var positionSide = side == OrderSide.Buy ? PositionSide.Buy : PositionSide.Sell;

            // Create new position
            var position = new PositionModel
            {
                Symbol = symbol,
                Side = positionSide, // Always set this based on order side
                Quantity = quantity,
                PositionValue = quantity * executionPrice,
                AveragePrice = executionPrice,
                MarkPrice = executionPrice, // Initial mark price
                Leverage = leverage,
                PositionStatus = PositionStatus.Normal,
                Direction = direction,
                PositionMode = _currentPositionMode,
                Category = Category.Linear, // Explicitly set Category
                CreateTime = now,
                UpdateTime = now
            };

            // Add to base positions dictionary
            _openPositions[positionKey] = position;
            _log.Information($"Created new position for {symbol} {direction} (Added to _openPositions)");

            // Calculate and track fees
            decimal fee = CalculateFee(quantity * executionPrice, false, false); // Fee for futures
            CurrencyPair tradingPair = CommonPairs.ParseSymbolToCurrencyPair(symbol);
            TrackFee(tradingPair.QuoteCoin, fee);

            // Publish position update
            var update = new PositionModelUpdate
            {
                Symbol = symbol,
                Side = positionSide, // Always set this based on order side
                Quantity = quantity,
                PositionValue = quantity * executionPrice,
                AveragePrice = executionPrice,
                MarkPrice = executionPrice,
                Leverage = leverage,
                PositionStatus = PositionStatus.Normal,
                Direction = direction,
                CreateTime = now,
                UpdateTime = now
            };

            // Update position tracking and trigger events
            HandlePositionUpdate(update);
        }

        /// <summary>
        /// Calculates the realized PNL for a closed position or partial close
        /// </summary>
        private decimal CalculateRealizedPnl(
            OrderSide positionSide,
            decimal closingSize,
            decimal entryPrice,
            decimal exitPrice)
        {
            if (positionSide == OrderSide.Buy)
            {
                // Long position: profit when exit price > entry price
                return closingSize * (exitPrice - entryPrice);
            }
            else
            {
                // Short position: profit when entry price > exit price
                return closingSize * (entryPrice - exitPrice);
            }
        }

        /// <summary>
        /// Handles position updates and updates tracking
        /// </summary>
        protected override void HandlePositionUpdate(PositionModelUpdate update)
        {
            // Use the base class implementation for tracking
            base.HandlePositionUpdate(update);

            // Log position update
            _log.Information($"Position update: {update.Symbol} {update.Direction}, Size: {update.Quantity}, Entry: {update.AveragePrice}, Mark: {update.MarkPrice}");
        }

        /// <summary>
        /// Gets the simulated account settings.
        /// </summary>
        public override async Task<AccountSettings> GetAccountSettingsAsync()
        {
            // Return the current cached/configured settings for the simulation
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                _log.Debug($"Returning simulated account settings: Mode={_currentPositionMode}, Margin={_currentMarginMode}, Trade={_currentTradeMode}, Lev={_currentBuyLeverage}x");
                return new AccountSettings
                {
                    PositionMode = _currentPositionMode,
                    MarginMode = _currentMarginMode,
                    TradeMode = _currentTradeMode,
                    BuyLeverage = _currentBuyLeverage,
                    SellLeverage = _currentSellLeverage
                };
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error in GetAccountSettingsAsync with simulated latency.");
                return new AccountSettings();
            }
        }

        /// <summary>
        /// Sets the simulated leverage.
        /// </summary>
        public override async Task<OperationResult> SetLeverageAsync(decimal? leverage, PositionDirection? positionDirection = null)
        {
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                _log.Information($"Setting simulated leverage state to {leverage}x (PosDir: {positionDirection}).");
                return await base.SetLeverageAsync(leverage, positionDirection);
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error in SetLeverageAsync with simulated latency.");
                return new OperationResult { IsSuccess = false, Message = $"Internal error: {ex.Message}" };
            }
        }

        // --- End Implementations for Abstract Base Methods ---

        /// <summary>
        /// Stops the exchange connection and unsubscribes from market data
        /// </summary>
        public override async Task StopAsync()
        {
            // If we're already stopping or stopped, just return
            if (State == ExchangeState.Stopping || State == ExchangeState.Stopped)
                return;

            State = ExchangeState.Stopping;
            _log.Information("Stopping simulated exchange...");

            try
            {
                // Cancel any pending operations
                _cts.Cancel();

                // Unsubscribe from market data
                _spotSubscription?.Dispose();
                _spotSubscription = null;

                _futuresSubscription?.Dispose();
                _futuresSubscription = null;

                State = ExchangeState.Stopped;
                _log.Information("Simulated exchange stopped successfully");
            }
            catch (Exception ex)
            {
                _log.Error($"Error stopping simulated exchange: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Basic API Methods - Initial Implementation

        /// <summary>
        /// Gets the balance for a specific asset
        /// </summary>
        /// <param name="asset">The asset to get the balance for</param>
        /// <returns>The balance of the specified asset</returns>
        public override async Task<AssetBalance> GetBalanceAsync(string asset)
        {
            if (string.IsNullOrEmpty(asset))
                throw new ArgumentNullException(nameof(asset));

            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                if (Helpers.TryParseCoin(asset, out var coinType))
                {
                    // Get the balance from the wallet
                    if (SimulatedWallet != null)
                    {
                        var (free, locked) = SimulatedWallet.GetExchangeBalance(coinType);
                        // Note: Shouldn't this calculation done already by wallet ?!
                        return new AssetBalance
                        {
                            Asset = asset,
                            Available = free,
                            InOrder = locked,
                            Total = free + locked
                        };
                    }
                    else
                    {
                        // Fallback to base wallet
                        // Note: Shouldn't we receive already a returnable AssetBalance from wallet already ?!
                        var balance = Wallet.GetBalance(coinType);
                        return new AssetBalance
                        {
                            Asset = asset,
                            Available = balance,
                            InOrder = 0,
                            Total = balance
                        };
                    }
                }
                else
                {
                    _log.Warning($"Invalid asset type: {asset}");
                    return new AssetBalance
                    {
                        Asset = asset,
                        Available = 0,
                        InOrder = 0,
                        Total = 0
                    };
                }
            }
            catch (Exception ex)
            {
                _log.Error($"Error getting balance for {asset}: {ex.Message}");
                return new AssetBalance
                {
                    Asset = asset,
                    Available = 0,
                    InOrder = 0,
                    Total = 0
                };
            }
        }

        /// <summary>
        /// Gets all asset balances
        /// </summary>
        /// <returns>Collection of all asset balances</returns>
        public override async Task<IEnumerable<AssetBalance>> GetBalancesAsync()
        {
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                var balances = new List<AssetBalance>();

                if (SimulatedWallet != null)
                {
                    // Get all balances from the simulated wallet
                    foreach (var balance in SimulatedWallet.GetAllBalances())
                    {
                        balances.Add(balance.Value);
                    }
                }
                else
                {
                    // Fallback to base wallet
                    foreach (var balance in Wallet.GetAllBalances())
                    {
                        balances.Add(balance.Value);
                    }
                }

                return balances;
            }
            catch (Exception ex)
            {
                _log.Error($"Error getting all balances: {ex.Message}");
                return Array.Empty<AssetBalance>();
            }
        }

        // --- Corrected Override GetOrderStatusAsync ---
        public override async Task<OrderModel?> GetOrderStatusAsync(string orderId, Category category, string? clientId = null)
        {
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                _log.Debug($"Getting order status - OrderId: {orderId}, ClientId: {clientId}, Category: {category}");

                lock (_lockObject)
                {
                    Func<OrderModel, bool> orderMatch = o =>
                        (o.OrderId == orderId) ||
                        (!string.IsNullOrEmpty(clientId) && o.ClientOrderId == clientId);

                    var completedOrder = _completedOrders.Values.FirstOrDefault(orderMatch);
                    if (completedOrder != null)
                    {
                        if (category != Category.Undefined && completedOrder.Category != category)
                        {
                            _log.Warning($"Order found in completed, but category mismatch (Expected: {category}, Found: {completedOrder.Category}) - OrderId: {orderId}, ClientId: {clientId}");
                            return null; // Return null for mismatch
                        }
                        _log.Debug($"Order found in completed orders - OrderId: {orderId}, ClientId: {clientId}");
                        return completedOrder;
                    }

                    var activeOrder = _activeOrders.Values.FirstOrDefault(orderMatch);
                    if (activeOrder != null)
                    {
                        if (category != Category.Undefined && activeOrder.Category != category)
                        {
                            _log.Warning($"Order found in active, but category mismatch (Expected: {category}, Found: {activeOrder.Category}) - OrderId: {orderId}, ClientId: {clientId}");
                            return null; // Return null for mismatch
                        }
                        _log.Debug($"Order found in active orders - OrderId: {orderId}, ClientId: {clientId}");
                        return activeOrder;
                    }
                }

                _log.Warning($"Order not found - OrderId: {orderId}, ClientId: {clientId}, Category: {category}");
                return null; // Not found
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error getting order status - OrderId: {orderId}, ClientId: {clientId}");
                return null; // Return null on error
            }
        }

        public override async Task<OperationResult> CancelOrderAsync(string symbol, string? orderId = null, string? clientId = null)
        {
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                _log.Debug($"Cancelling order {orderId} for symbol {symbol}");

                // Check if the order is in active orders (inherited from base)
                if (_activeOrders.TryGetValue(orderId, out var activeOrder))
                {
                    // Check if the symbol matches
                    if (activeOrder.Symbol != symbol)
                    {
                        _log.Warning($"Order {orderId} symbol mismatch: expected {symbol}, found {activeOrder.Symbol}");
                        return new OperationResult() { IsSuccess = false, Message = $"Order {orderId} symbol mismatch: expected {symbol}, found {activeOrder.Symbol}" };
                    }

                    // Check if the order is already in a final state (shouldn't be in _activeOrders, but check anyway)
                    if (IsOrderInFinalState(activeOrder.Status))
                    {
                        _log.Warning($"Attempted to cancel order {orderId} which is already in final state {activeOrder.Status}");
                        return new OperationResult() { IsSuccess = false, Message = $"Order {orderId} is already in final state {activeOrder.Status}" };
                    }

                    // --- Release Locked Funds ---
                    var simulatedWallet = Wallet as SimulatedWallet;
                    if (simulatedWallet == null)
                    {
                        _log.Error($"Simulated wallet not available for releasing funds.");
                        // Continue with cancellation, but log the error
                    }
                    else
                    {
                        try
                        {
                            CurrencyPair pair = CommonPairs.ParseSymbolToCurrencyPair(activeOrder.Symbol);
                            if (activeOrder.Category == Category.Spot)
                            {
                                // Release Spot funds
                                if (activeOrder.Side == OrderSide.Buy && activeOrder.Price.HasValue)
                                {
                                    simulatedWallet.ReleaseFunds(pair.QuoteCoin, activeOrder.Price.Value * activeOrder.Quantity);
                                }
                                else if (activeOrder.Side == OrderSide.Sell)
                                {
                                    simulatedWallet.ReleaseFunds(pair.BaseCoin, activeOrder.Quantity);
                                }
                            }
                            else if (activeOrder.Category == Category.Linear && activeOrder.Price.HasValue)
                            {
                                // Release Futures Margin
                                decimal leverage = activeOrder.IsLeverage.HasValue && activeOrder.IsLeverage.Value
                                                    ? (activeOrder.Side == OrderSide.Buy ? _currentBuyLeverage : _currentSellLeverage)
                                                    : 1m;
                                decimal requiredMargin = (activeOrder.Quantity * activeOrder.Price.Value) / leverage;
                                simulatedWallet.ReleaseFunds(pair.QuoteCoin, requiredMargin);
                            }
                        }
                        catch (Exception ex)
                        {
                            _log.Error(ex, $"Error releasing funds for cancelled order {orderId}");
                            // Continue with cancellation despite fund release error
                        }
                    }
                    // --- End Release Locked Funds ---

                    // Update the order status locally (before triggering update)
                    activeOrder.Status = OrderStatus.Cancelled;
                    activeOrder.UpdateTime = DateTime.UtcNow;

                    // Create an order update to trigger events & update tracking
                    var orderUpdate = CreateOrderUpdateFromModel(activeOrder);
                    orderUpdate.Status = OrderStatus.Cancelled; // Ensure status is Cancelled

                    // Update order tracking (this will remove from _activeOrders) and trigger events
                    HandleOrderUpdate(orderUpdate);

                    // Add to completed orders history
                    _completedOrders[orderId] = activeOrder; // Store the final state

                    _log.Information($"Cancelled order {orderId}");
                    return new OperationResult() { IsSuccess = true, Message = $"Cancelled order {orderId}" };
                }

                // Order not found in active orders - check completed?
                if (_completedOrders.ContainsKey(orderId))
                {
                     _log.Warning($"Order {orderId} not active, already completed/cancelled.");
                     return new OperationResult() { IsSuccess = false, Message = $"Order {orderId} not active." };
                }

                // Order truly not found
                _log.Warning($"Order {orderId} not found for cancellation.");
                return new OperationResult() { IsSuccess = false, Message = $"Order {orderId} not found." };
            }
            catch (Exception ex)
            {
                _log.Error($"Error cancelling order: {ex.Message}");
                return new OperationResult() { IsSuccess = false, Message = $"Error cancelling order: {ex.Message}" };
            }
        }

        public override async Task<OperationResult> CancelAllOrdersAsync(string? symbol = null, Category? category = null)
        {
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                _log.Debug($"Cancelling all orders for symbol {symbol ?? "All Symbols"}, category {category?.ToString() ?? "All Categories"}");

                var toCancel = new List<OrderModel>();

                lock (_lockObject)
                {
                    foreach (var order in _activeOrders.Values.ToList())
                    {
                        if ((symbol == null || order.Symbol == symbol) &&
                            (category == null || order.Category == category))
                        {
                            // Only cancel orders not in a final state
                            if (!IsOrderInFinalState(order.Status))
                            {
                                toCancel.Add(order);
                            }
                        }
                    }

                    foreach (var order in toCancel)
                    {
                        // --- Release Locked Funds ---
                        var simulatedWallet = Wallet as SimulatedWallet;
                        if (simulatedWallet != null)
                        {
                            try
                            {
                                CurrencyPair pair = CommonPairs.ParseSymbolToCurrencyPair(order.Symbol);
                                if (order.Category == Category.Spot)
                                {
                                    if (order.Side == OrderSide.Buy && order.Price.HasValue)
                                    {
                                        simulatedWallet.ReleaseFunds(pair.QuoteCoin, order.Price.Value * order.Quantity);
                                    }
                                    else if (order.Side == OrderSide.Sell)
                                    {
                                        simulatedWallet.ReleaseFunds(pair.BaseCoin, order.Quantity);
                                    }
                                }
                                else if (order.Category == Category.Linear && order.Price.HasValue)
                                {
                                    decimal leverage = order.IsLeverage.HasValue && order.IsLeverage.Value
                                        ? (order.Side == OrderSide.Buy ? _currentBuyLeverage : _currentSellLeverage)
                                        : 1m;
                                    decimal requiredMargin = (order.Quantity * order.Price.Value) / leverage;
                                    simulatedWallet.ReleaseFunds(pair.QuoteCoin, requiredMargin);
                                }
                            }
                            catch (Exception ex)
                            {
                                _log.Error(ex, $"Error releasing funds for cancelled order {order.OrderId}");
                            }
                        }
                        // --- End Release Locked Funds ---

                        order.Status = OrderStatus.Cancelled;
                        order.UpdateTime = DateTime.UtcNow;

                        var orderUpdate = CreateOrderUpdateFromModel(order);
                        orderUpdate.Status = OrderStatus.Cancelled;

                        HandleOrderUpdate(orderUpdate);
                        _completedOrders[order.OrderId] = order;
                    }
                }

                _log.Information($"Cancelled {toCancel.Count} orders.");
                return new OperationResult() { IsSuccess = true, Message = $"Cancelled {toCancel.Count} orders" };
            }
            catch (Exception ex)
            {
                _log.Error($"Error cancelling all orders: {ex.Message}");
                return new OperationResult() { IsSuccess = false, Message = $"Error cancelling all orders: {ex.Message}" };
            }
        }

        // TODO / Note: This could be also general anough to be defined in BaseExchangeAPI, except needs to be decided if SimulatedWallet can be updated from BaseExchangeAPI
        // Alternatively: BaseExchangeAPI is just maximally "declarations/signatures only" !
        protected override async Task<SpotOrderResult> PlaceSpotOrderAsync(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal? price = null,
            OrderType? orderType = null,
            TimeInForce timeInForce = TimeInForce.GoodTillCancel,
            string? clientId = null)
        {
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                // Use order type from config if not specified
                orderType ??= _config.OrderDefaults.SpotOrderType;

                _log.Debug($"Placing spot order: Client={clientId}, {side} {quantity} {symbol} @ {price ?? 0} (Type: {orderType})");

                // Validate inputs
                if (quantity <= 0)
                {
                    return CreateSpotErrorResult("Order quantity must be greater than zero");
                }

                if (orderType == OrderType.Limit && price == null)
                {
                    return CreateSpotErrorResult("Limit orders must specify a price");
                }

                // Get current market data
                if (_latestSpotData == null)
                {
                    _log.Error("No market data available for execution");
                    return CreateSpotErrorResult("No market data available for execution");
                }

                // Get the wallet as SimulatedWallet
                if (SimulatedWallet == null)
                {
                    _log.Error("Simulated wallet not available");
                    return CreateSpotErrorResult("Simulated wallet not available");
                }

                // Determine execution price
                decimal executionPrice;
                if (orderType == OrderType.Market)
                {
                    // For market orders, determine base price (slippage applied later if enabled)
                    decimal basePrice;
                    if (side == OrderSide.Buy && _latestSpotData.LowestAsk > 0)
                    {
                        basePrice = _latestSpotData.LowestAsk;
                    }
                    else if (side == OrderSide.Sell && _latestSpotData.HighestBid > 0)
                    {
                        basePrice = _latestSpotData.HighestBid;
                    }
                    else // Fallback if Bid/Ask missing
                    {
                        basePrice = _latestSpotData.LastPrice; // Use LastPrice as fallback
                        if (basePrice == 0)
                        {
                            _log.Error("Cannot determine market execution price (Missing Ask/Bid/Last)");
                            return CreateSpotErrorResult("Cannot determine market execution price");
                        }
                    }
                    executionPrice = basePrice; // Start with base price

                    // --- Apply Slippage ONLY if enabled ---
                    if (_simConfig.SimulateSlippage)
                    {
                        if (side == OrderSide.Buy)
                        {
                            executionPrice = basePrice * (1 + _simConfig.MarketOrderSlippage);
                            _log.Debug($"Applied Spot Market Buy Slippage: Base={basePrice}, Slippage={_simConfig.MarketOrderSlippage * 100}%, Final={executionPrice}");
                        }
                        else // Sell
                        {
                            executionPrice = basePrice * (1 - _simConfig.MarketOrderSlippage);
                            _log.Debug($"Applied Spot Market Sell Slippage: Base={basePrice}, Slippage={_simConfig.MarketOrderSlippage * 100}%, Final={executionPrice}");
                        }
                    }
                    // --- End Slippage ---
                }
                else // Limit Order
                {
                    // For limit orders, use the specified price
                    executionPrice = price!.Value;

                    // Check if the limit price is valid for immediate execution
                    bool wouldExecuteImmediately = side == OrderSide.Buy
                        ? executionPrice >= _latestSpotData.LowestAsk
                        : executionPrice <= _latestSpotData.HighestBid;

                    // --- FOK / IOC / PostOnly Logic ---
                    if (timeInForce == TimeInForce.FillOrKill)
                    {
                        if (!wouldExecuteImmediately)
                        {
                            _log.Information($"Rejecting FOK spot order {side} {quantity} {symbol} @ {executionPrice} as it cannot fill immediately.");
                            return CreateSpotErrorResult("Order rejected: FOK order cannot fill immediately.", OrderStatus.Rejected); // Explicitly Rejected
                        }
                        // If wouldExecuteImmediately, fall through to ExecuteSpotOrder block
                    }
                    else if (timeInForce == TimeInForce.PostOnly && wouldExecuteImmediately)
                    {
                         _log.Information($"Rejecting PostOnly spot order {side} {quantity} {symbol} @ {executionPrice} as it would execute immediately.");
                         return CreateSpotErrorResult("Order rejected: PostOnly order would execute immediately.", OrderStatus.Rejected); // Explicitly Rejected
                    }
                    // TODO: Add IOC logic if needed

                    // --- Create Pending Order Logic ---
                    // Only create pending if it won't execute now AND it's not FOK/PostOnly
                    if (!wouldExecuteImmediately &&
                        timeInForce != TimeInForce.FillOrKill &&
                        timeInForce != TimeInForce.PostOnly)
                    {
                        _log.Information($"Creating pending limit order: Client={clientId}, {side} {quantity} {symbol} @ {executionPrice}, TIF={timeInForce}");
                        return CreatePendingSpotOrder(symbol, side, quantity, executionPrice, orderType.Value, timeInForce, clientId);
                    }

                    // If it would execute immediately (and wasn't FOK/PostOnly that got rejected above),
                    // or if it's a GTC/IOC that would execute immediately, fall through.
                    // executionPrice is already set to the limit price.
                }

                // --- Wallet Check ---
                // Calculate the quote amount
                decimal quoteAmount = quantity * executionPrice; // Use final execution price

                // Check if the user has enough balance
                CoinType baseCoin = TradingPair.BaseCoin;
                CoinType quoteCoin = TradingPair.QuoteCoin;

                if (side == OrderSide.Buy)
                {
                    // For buy orders, check quote currency (e.g., USDT)
                    var (free, _) = SimulatedWallet.GetExchangeBalance(quoteCoin);
                    if (free < quoteAmount)
                    {
                        _log.Warning($"Insufficient {quoteCoin} balance. Required: {quoteAmount}, Available: {free}");
                        return CreateSpotErrorResult($"Insufficient {quoteCoin} balance. Required: {quoteAmount}, Available: {free}");
                    }

                    // Lock the funds
                    if (!SimulatedWallet.LockFunds(quoteCoin, quoteAmount))
                    {
                        _log.Error($"Failed to lock {quoteAmount} {quoteCoin}");
                        return CreateSpotErrorResult($"Failed to lock {quoteAmount} {quoteCoin}");
                    }
                }
                else // Sell
                {
                    // For sell orders, check base currency (e.g., BTC)
                    var (free, _) = SimulatedWallet.GetExchangeBalance(baseCoin);
                    if (free < quantity)
                    {
                        _log.Warning($"Insufficient {baseCoin} balance. Required: {quantity}, Available: {free}");
                        return CreateSpotErrorResult($"Insufficient {baseCoin} balance. Required: {quantity}, Available: {free}");
                    }

                    // Lock the funds
                    if (!SimulatedWallet.LockFunds(baseCoin, quantity))
                    {
                        _log.Error($"Failed to lock {quantity} {baseCoin}");
                        return CreateSpotErrorResult($"Failed to lock {quantity} {baseCoin}");
                    }
                }

                // Execute the order immediately for market orders or executable limit orders
                _log.Information($"Executing order: Client={clientId}, {side} {quantity} {symbol} @ {executionPrice}");
                // Pass the potentially slippage-adjusted executionPrice
                return ExecuteSpotOrder(symbol, side, quantity, executionPrice, orderType ?? OrderType.Market, null, clientId); // Pass null orderId for new exec
            }
            catch (Exception ex)
            {
                _log.Error($"Error placing spot order: {ex.Message}");
                return CreateSpotErrorResult($"Internal error: {ex.Message}");
            }
        }

        private SpotOrderResult CreatePendingSpotOrder(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal price,
            OrderType orderType,
            TimeInForce timeInForce,
            string? clientId)
        {
            try
            {
                // Calculate the quote amount
                decimal quoteAmount = quantity * price;

                // Get the wallet as SimulatedWallet
                var simulatedWallet = Wallet as SimulatedWallet;
                if (simulatedWallet == null)
                {
                    _log.Error("Simulated wallet not available");
                    return CreateSpotErrorResult("Simulated wallet not available");
                }

                // Lock the funds
                CoinType baseCoin = TradingPair.BaseCoin;
                CoinType quoteCoin = TradingPair.QuoteCoin;

                if (side == OrderSide.Buy)
                {
                    // For buy orders, lock the quote currency (e.g., USDT)
                    if (!simulatedWallet.LockFunds(quoteCoin, quoteAmount))
                    {
                        _log.Error($"Failed to lock {quoteAmount} {quoteCoin}");
                        return CreateSpotErrorResult($"Failed to lock {quoteAmount} {quoteCoin}");
                    }
                }
                else // Sell
                {
                    // For sell orders, lock the base currency (e.g., BTC)
                    if (!simulatedWallet.LockFunds(baseCoin, quantity))
                    {
                        _log.Error($"Failed to lock {quantity} {baseCoin}");
                        return CreateSpotErrorResult($"Failed to lock {quantity} {baseCoin}");
                    }
                }

                // Generate a unique order ID
                string orderId = GenerateOrderId();

                // Create an order model
                var order = new OrderModel
                {
                    OrderId = orderId,
                    ClientOrderId = clientId,
                    Symbol = symbol,
                    Side = side,
                    Quantity = quantity,
                    Price = price,
                    OrderType = orderType,
                    Status = OrderStatus.New,
                    Category = Category.Spot,
                    CreateTime = DateTime.UtcNow,
                    UpdateTime = DateTime.UtcNow,
                    TimeInForce = timeInForce // Store TimeInForce
                };

                // Add to active orders (inherited from base class)
                // TrackOrder will handle adding to _activeOrders if not final state
                TrackOrder(order);

                // Calculate and track fees for placing the limit order (maker fee)
                // Note: Fees are typically charged on execution, but we track potential fee here
                // decimal fee = CalculateFee(quantity * price, true, true); // Maker fee for limit orders - Let's calculate fee on execution instead
                // CurrencyPair tradingPair = CommonPairs.ParseSymbolToCurrencyPair(symbol);
                // TrackFee(tradingPair.QuoteCoin, fee); // Track fee on execution

                // Create an order update to trigger events
                var orderUpdate = new OrderModelUpdate
                {
                    OrderId = orderId,
                    ClientOrderId = clientId,
                    Symbol = symbol,
                    Side = side,
                    Quantity = quantity,
                    Price = price,
                    OrderType = orderType,
                    Status = OrderStatus.New,
                    Category = Category.Spot,
                    CreateTime = order.CreateTime,
                    UpdateTime = DateTime.UtcNow,
                    AveragePrice = price,
                    QuantityFilled = 0,
                    ValueFilled = 0,
                    // Set the fee information
                    ExecutedFee = null // Fee is calculated on execution
                };

                // Update order tracking and trigger events
                HandleOrderUpdate(orderUpdate);

                // Create and return the result
                return new SpotOrderResult
                {
                    IsSuccess = true,
                    OrderId = orderId,
                    ClientOrderId = clientId,
                    Status = OrderStatus.New,
                    Timestamp = DateTime.UtcNow,
                    Message = "Order placed successfully"
                };
            }
            catch (Exception ex)
            {
                _log.Error($"Error creating pending order: {ex.Message}");
                return CreateSpotErrorResult($"Error creating pending order: {ex.Message}");
            }
        }

        private SpotOrderResult ExecuteSpotOrder(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal executionPrice,
            OrderType orderType,
            string? orderId = null, // OrderId is optional for new execution
            string? clientId = null)
        {
            bool isExistingOrder = !string.IsNullOrEmpty(orderId);
            string orderIdToUse = isExistingOrder ? orderId! : GenerateOrderId();
            OrderModel? existingOrderModel = null;
            DateTime createTime = DateTime.UtcNow; // Default for new orders
            string? effectiveClientId = clientId;

            // If it's an existing order (limit order being triggered), retrieve its model
            if (isExistingOrder)
            {
                if (_activeOrders.TryGetValue(orderIdToUse, out existingOrderModel))
                {
                     createTime = existingOrderModel.CreateTime; // Use original creation time
                     _log.Debug($"Executing existing spot order {orderIdToUse}.");
                     effectiveClientId = existingOrderModel.ClientOrderId ?? clientId; // Use effective ClientId
                }
                else
                {
                    _log.Error($"Cannot execute spot order. Order ID {orderIdToUse} provided but not found in active orders.");
                    // Cannot proceed without the order model if ID was provided
                    return CreateSpotErrorResult($"Order ID {orderIdToUse} not found in active orders.");
                }
            }
            else
            {
                 _log.Debug($"Executing new spot order {orderIdToUse}.");
            }

            // --- Fund Movement ---
            decimal quoteAmount = quantity * executionPrice;
            CoinType baseCoin = TradingPair.BaseCoin;
            CoinType quoteCoin = TradingPair.QuoteCoin;

            // Check if wallet is available
             if (SimulatedWallet == null)
             {
                 _log.Error($"Simulated wallet not available during execution of order {orderIdToUse}.");
                 // Mark as rejected?
                 // MarkOrderAsRejected(orderIdToUse, "Wallet unavailable");
                 return CreateSpotErrorResult("Simulated wallet not available");
             }

            try
            {
                if (side == OrderSide.Buy)
                {
                    // Consume locked quote currency or deduct directly if market order
                    if (isExistingOrder) // Limit order triggered
                        SimulatedWallet.ConsumeFunds(quoteCoin, quoteAmount); // Consume pre-locked funds
                    else // New market/immediate limit order
                    {
                        if (!SimulatedWallet.DeductBalance(quoteCoin, quoteAmount)) // Try deducting directly
                        {
                            _log.Warning($"Insufficient {quoteCoin} for order {orderIdToUse}. Required: {quoteAmount}");
                            // MarkOrderAsRejected(orderIdToUse, $"Insufficient {quoteCoin}");
                            return CreateSpotErrorResult($"Insufficient {quoteCoin}");
                        }
                    }
                    // Add base currency
                    SimulatedWallet.Add(baseCoin, quantity);
                }
                else // Sell
                {
                    // Consume locked base currency or deduct directly
                    if (isExistingOrder) // Limit order triggered
                        SimulatedWallet.ConsumeFunds(baseCoin, quantity); // Consume pre-locked funds
                    else // New market/immediate limit order
                    {
                        if (!SimulatedWallet.DeductBalance(baseCoin, quantity)) // Try deducting directly
                        {
                            _log.Warning($"Insufficient {baseCoin} for order {orderIdToUse}. Required: {quantity}");
                             // MarkOrderAsRejected(orderIdToUse, $"Insufficient {baseCoin}");
                            return CreateSpotErrorResult($"Insufficient {baseCoin}");
                        }
                    }
                    // Add quote currency
                    SimulatedWallet.Add(quoteCoin, quoteAmount);
                }
            }
            catch (Exception ex)
            {
                 _log.Error(ex, $"Error processing wallet funds for order {orderIdToUse}");
                 // MarkOrderAsRejected(orderIdToUse, $"Wallet funds error: {ex.Message}");
                 return CreateSpotErrorResult($"Wallet funds error: {ex.Message}");
            }
            // --- End Fund Movement ---

            // --- Order Model Update ---
            // Create or update the order model to reflect the fill
            var filledOrder = existingOrderModel ?? new OrderModel { OrderId = orderIdToUse, CreateTime = createTime };
            filledOrder.ClientOrderId = effectiveClientId; // Use effective ClientId
            filledOrder.Symbol = symbol;
            filledOrder.Side = side;
            filledOrder.Quantity = quantity;
            filledOrder.Price = executionPrice; // For limit, this was the limit price. For market, the simulated fill price.
            filledOrder.OrderType = orderType;
            filledOrder.Status = OrderStatus.Filled; // Final state
            filledOrder.Category = Category.Spot;
            filledOrder.UpdateTime = DateTime.UtcNow;
            filledOrder.AveragePrice = executionPrice;
            filledOrder.QuantityFilled = quantity;
            filledOrder.ValueFilled = quoteAmount;
            filledOrder.QuantityRemaining = 0;
            filledOrder.ValueRemaining = 0;
            filledOrder.TimeInForce = existingOrderModel?.TimeInForce ?? TimeInForce.GoodTillCancel; // Preserve TIF or default

            // Calculate and track fees (Taker fee for market/immediate limit, Maker potentially for triggered limit)
            // Assuming triggered limit orders (isExistingOrder=true) get Maker fees if TIF wasn't IOC/FOK
            // A more robust check might look at the original TimeInForce stored in existingOrderModel if needed
            bool isMaker = isExistingOrder && orderType == OrderType.Limit && filledOrder.TimeInForce != TimeInForce.ImmediateOrCancel && filledOrder.TimeInForce != TimeInForce.FillOrKill;
            decimal feeAmount = CalculateFee(quoteAmount, isMaker, true); // Spot fee
            CurrencyPair tradingPair = CommonPairs.ParseSymbolToCurrencyPair(symbol);
            TrackFee(tradingPair.QuoteCoin, feeAmount); // Track fee
            filledOrder.ExecutedFee = new Fee(
                new CurrencyAmount(tradingPair.BaseCoin, 0),
                new CurrencyAmount(tradingPair.QuoteCoin, feeAmount)
            );
            // --- End Order Model Update ---

            // Add to completed orders history
            _completedOrders[orderIdToUse] = filledOrder;

            // Create an order update to trigger events & update tracking in base class
            var orderUpdate = CreateOrderUpdateFromModel(filledOrder); // Use helper

            // Update order tracking (HandleOrderUpdate will remove from _activeOrders because status is Filled)
            HandleOrderUpdate(orderUpdate);

            // Create and return the result
            return new SpotOrderResult
            {
                IsSuccess = true,
                OrderId = orderIdToUse,
                ExecutedPrice = executionPrice,
                ExecutedQuantity = quantity,
                Status = OrderStatus.Filled,
                Timestamp = filledOrder.UpdateTime, // Use final update time
                Message = "Order executed successfully"
            };
        }

        private string GenerateOrderId()
        {
            return $"SIM{Interlocked.Increment(ref _orderIdCounter)}";
        }

        private SpotOrderResult CreateSpotErrorResult(string message, OrderStatus status = OrderStatus.Rejected) // Added optional status
        {
            return new SpotOrderResult
            {
                IsSuccess = false,
                OrderId = string.Empty,
                ExecutedPrice = null,
                ExecutedQuantity = null,
                Status = status, // Use provided status
                Timestamp = DateTime.UtcNow,
                Message = message
            };
        }

        protected override async Task<FuturesOrderResult> PlaceFuturesOrderAsync(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal? price = null,
            decimal? leverage = null,
            OrderType? orderType = null,
            PositionDirection? positionDirection = null,
            TimeInForce timeInForce = TimeInForce.GoodTillCancel, // Added timeInForce
            bool reduceOnly = false,
            string? clientId = null)
        {
            if (State != ExchangeState.Ready)
                return CreateFuturesErrorResult("API not in Ready state"); // No await Task.Delay if not ready

            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                // Validate inputs
                if (string.IsNullOrEmpty(symbol))
                    return CreateFuturesErrorResult("Symbol cannot be empty");

                if (quantity <= 0)
                    return CreateFuturesErrorResult("Quantity must be greater than zero");

                // Use order type from config if not specified
                orderType ??= _config.OrderDefaults.FuturesOrderType;

                // Get the latest market data
                if (_latestFuturesData == null || _latestFuturesData.Symbol != symbol)
                {
                    _log.Warning($"No market data available for {symbol}");
                    return CreateFuturesErrorResult($"No market data available for {symbol}");
                }

                // Determine *initial* execution price suggestion (slippage will be applied later if market)
                decimal initialExecutionPriceSuggestion;

                if (orderType == OrderType.Market)
                {
                    // For market orders, suggest the current mark price as a base before slippage
                    initialExecutionPriceSuggestion = _latestFuturesData.MarkPrice!.Value;
                }
                else if (orderType == OrderType.Limit && price.HasValue)
                {
                    // For limit orders, check if they can be executed immediately
                    bool wouldExecuteImmediately = (side == OrderSide.Buy && _latestFuturesData.MarkPrice <= price.Value) ||
                                                 (side == OrderSide.Sell && _latestFuturesData.MarkPrice >= price.Value);

                    // --- FOK / IOC / PostOnly Logic ---
                    if (timeInForce == TimeInForce.FillOrKill)
                    {
                        if (!wouldExecuteImmediately)
                        {
                            _log.Information($"Rejecting FOK futures order {side} {quantity} {symbol} @ {price.Value} as it cannot fill immediately.");
                            return CreateFuturesErrorResult("Order rejected: FOK order cannot fill immediately.", OrderStatus.Rejected); // Explicitly Rejected
                        }
                        // If wouldExecuteImmediately, fall through to ExecuteFuturesOrder block
                    }
                    else if (timeInForce == TimeInForce.PostOnly && wouldExecuteImmediately)
                    {
                        _log.Information($"Rejecting PostOnly futures order {side} {quantity} {symbol} @ {price.Value} as it would execute immediately.");
                        return CreateFuturesErrorResult("Order rejected: PostOnly order would execute immediately.", OrderStatus.Rejected); // Explicitly Rejected
                    }
                     // TODO: Add IOC logic if needed

                    // --- Create Pending Order Logic ---
                    // Only create pending if it won't execute now AND it's not FOK/PostOnly
                    if (!wouldExecuteImmediately &&
                        timeInForce != TimeInForce.FillOrKill &&
                        timeInForce != TimeInForce.PostOnly)
                    {
                        _log.Information($"Creating pending limit order: Client={clientId}, {side} {quantity} {symbol} @ {price.Value}, TIF={timeInForce}, ReduceOnly={reduceOnly}");
                        return CreatePendingFuturesOrder(symbol, side, quantity, price.Value, orderType.Value, positionDirection, leverage, timeInForce, reduceOnly, clientId);
                    }

                    // If it would execute immediately (and wasn't FOK/PostOnly that got rejected above),
                    // or if it's a GTC/IOC that would execute immediately, fall through.
                    initialExecutionPriceSuggestion = price.Value; // Use limit price for execution
                }
                else if (orderType == OrderType.Limit && !price.HasValue) // Explicit check for missing limit price
                {
                    _log.Warning($"Price is required for Limit orders");
                    return CreateFuturesErrorResult($"Price is required for Limit orders");
                }
                else // Should catch any other unhandled cases or invalid combinations
                {
                    _log.Warning($"Unsupported order type ({orderType}) or missing price for placement.");
                    return CreateFuturesErrorResult($"Unsupported order type ({orderType}) or missing price.");
                }

                // Get the leverage from the current settings or use the provided one
                decimal effectiveLeverage = leverage ?? (side == OrderSide.Buy ? _currentBuyLeverage : _currentSellLeverage);

                // Validate leverage against config's MaxLeverage
                if (effectiveLeverage < 1 || effectiveLeverage > _simConfig.MaxLeverage)
                {
                    _log.Error($"Invalid leverage: {effectiveLeverage}. Must be between 1 and {_simConfig.MaxLeverage}");
                    return CreateFuturesErrorResult($"Invalid leverage: {effectiveLeverage}. Must be between 1 and {_simConfig.MaxLeverage}");
                }

                // Execute the order immediately, passing the suggested price (slippage applied inside ExecuteFuturesOrder if market)
                _log.Information($"Executing futures order: Client={clientId}, {side} {quantity} {symbol} @ {initialExecutionPriceSuggestion} (initial) with {effectiveLeverage}x leverage, ReduceOnly={reduceOnly}");
                // Pass ReduceOnly
                // ExecuteFuturesOrder will handle applying slippage for market orders internally
                return ExecuteFuturesOrder(symbol, side, quantity, initialExecutionPriceSuggestion, effectiveLeverage, orderType.Value, positionDirection, null, reduceOnly, clientId); // Pass null orderId for new exec
            }
            catch (Exception ex)
            {
                _log.Error($"Error placing futures order: {ex.Message}");
                return CreateFuturesErrorResult($"Internal error: {ex.Message}");
            }
        }

        private FuturesOrderResult CreatePendingFuturesOrder(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal price,
            OrderType orderType,
            PositionDirection? positionDirection, // TODO / Note: This is not used ?!
            decimal? leverage = null,
            TimeInForce timeInForce = TimeInForce.GoodTillCancel, // Added timeInForce
            bool reduceOnly = false,
            string? clientId = null)
        {
            try // Outer catch block will handle ArgumentException from parsing now
            {
                // Get the leverage from the current settings or use the provided one
                decimal effectiveLeverage = leverage ?? (side == OrderSide.Buy ? _currentBuyLeverage : _currentSellLeverage);

                // Validate leverage against config's MaxLeverage
                if (effectiveLeverage < 1 || effectiveLeverage > _simConfig.MaxLeverage)
                {
                    _log.Error($"Invalid leverage: {effectiveLeverage}. Must be between 1 and {_simConfig.MaxLeverage}");
                    return CreateFuturesErrorResult($"Invalid leverage: {effectiveLeverage}. Must be between 1 and {_simConfig.MaxLeverage}");
                }

                // Calculate the notional value
                decimal notionalValue = quantity * price;

                // Check minimum notional value (typically exchanges have a minimum)
                decimal minimumNotional = 5m; // 5 USDT is a common minimum
                if (notionalValue < minimumNotional)
                {
                    _log.Error($"Order value too small: {notionalValue}. Minimum is {minimumNotional}");
                    return CreateFuturesErrorResult($"Order value too small: {notionalValue}. Minimum is {minimumNotional}");
                }

                // Check if SimulatedWallet is available
                if (SimulatedWallet == null)
                {
                    _log.Error("Simulated wallet not available");
                    return CreateFuturesErrorResult("Simulated wallet not available");
                }

                // Calculate the required margin
                decimal requiredMargin = notionalValue / effectiveLeverage;

                // Get the quote currency from the symbol (e.g., BTCUSDT -> USDT)
                // Removed inline try-catch for parsing
                CurrencyPair parsedPairForPending = CommonPairs.ParseSymbolToCurrencyPair(symbol);
                CoinType quoteCoinForPending = parsedPairForPending.QuoteCoin; // Get QuoteCoin from parsed pair

                // Check if user has enough balance for the margin
                if (SimulatedWallet.GetBalance(quoteCoinForPending) < requiredMargin)
                {
                    _log.Error($"Insufficient {quoteCoinForPending} balance for margin requirement. Required: {requiredMargin}, Available: {SimulatedWallet.GetBalance(quoteCoinForPending)}");
                    return CreateFuturesErrorResult($"Insufficient {quoteCoinForPending} balance for margin requirement");
                }

                // Lock the margin by deducting from available balance
                if (!SimulatedWallet.DeductBalance(quoteCoinForPending, requiredMargin))
                {
                    _log.Error($"Failed to lock {requiredMargin} {quoteCoinForPending} for margin");
                    return CreateFuturesErrorResult($"Insufficient {quoteCoinForPending} balance for margin requirement");
                }

                // Generate a unique order ID
                string orderId = GenerateOrderId();

                // Create the order model
                var order = new OrderModel
                {
                    OrderId = orderId,
                    ClientOrderId = clientId, // Store clientId
                    Symbol = symbol,
                    Side = side,
                    Quantity = quantity,
                    Price = price,
                    OrderType = orderType,
                    Status = OrderStatus.New,
                    Category = Category.Linear,
                    CreateTime = DateTime.UtcNow,
                    UpdateTime = DateTime.UtcNow,
                    TimeInForce = timeInForce, // Store TimeInForce
                    IsLeverage = true,
                    IsReduceOnly = reduceOnly, // Store ReduceOnly
                    PositionDirection = positionDirection // Store PositionDirection
                };

                // Add to pending orders - REMOVED, TrackOrder handles _activeOrders
                // _pendingOrders[orderId] = order;

                // Track the order in the base class (adds to _activeOrders if Status is New)
                TrackOrder(order);

                // Calculate and track fees
                decimal fee = CalculateFee(quantity * price, false, false);
                CurrencyPair tradingPair = CommonPairs.ParseSymbolToCurrencyPair(symbol); // Reparse needed here? Could pass parsedPairForPending
                TrackFee(tradingPair.QuoteCoin, fee);

                // Create an order update to trigger events
                var orderUpdate = new OrderModelUpdate
                {
                    OrderId = orderId,
                    ClientOrderId = clientId, // Use clientId
                    Symbol = symbol,
                    Side = side,
                    Quantity = quantity,
                    Price = price,
                    OrderType = orderType,
                    Status = OrderStatus.New,
                    Category = Category.Linear,
                    CreateTime = order.CreateTime,
                    UpdateTime = DateTime.UtcNow,
                    AveragePrice = price,
                    QuantityFilled = 0,
                    ValueFilled = 0,
                    // Set the fee information
                    ExecutedFee = new Fee(
                        new CurrencyAmount(tradingPair.BaseCoin, 0), // No base fee
                        new CurrencyAmount(tradingPair.QuoteCoin, fee)), // Quote fee
                    PositionDirection = positionDirection // Add PositionDirection here
                };

                // Update order tracking and trigger events
                HandleOrderUpdate(orderUpdate);

                // Return the result
                return new FuturesOrderResult
                {
                    IsSuccess = true,
                    OrderId = orderId,
                    Status = OrderStatus.New,
                    Timestamp = DateTime.UtcNow,
                    Message = "Limit order placed successfully",
                    Leverage = effectiveLeverage
                };
            }
            catch (Exception ex) // Outer catch block handles all exceptions now, including ArgumentException from parsing
            {
                _log.Error(ex, $"Error creating pending futures order: {ex.Message}");
                return CreateFuturesErrorResult($"Error creating pending futures order: {ex.Message}");
            }
        }

        private FuturesOrderResult CreateFuturesErrorResult(string message, OrderStatus status = OrderStatus.Rejected) // Added optional status
        {
            return new FuturesOrderResult
            {
                IsSuccess = false,
                OrderId = string.Empty,
                ExecutedPrice = null,
                ExecutedQuantity = null,
                Status = status, // Use provided status
                Timestamp = DateTime.UtcNow,
                Message = message,
                Leverage = 0.0m
            };
        }

        #endregion

        #region IDisposable

        public override void Dispose()
        {
            if (!_isDisposed)
            {
                // Release managed resources
                _cts.Cancel();
                _cts.Dispose();
                _spotSubscription?.Dispose();
                _futuresSubscription?.Dispose();

                _isDisposed = true;
            }

            base.Dispose();
        }

        #endregion

        #region Margin and Liquidation Helpers

        /// <summary>
        /// Calculates the total maintenance margin required for all open linear positions.
        /// </summary>
        /// <returns>The sum of maintenance margins for all open linear positions.</returns>
        private decimal GetTotalMaintenanceMargin()
        {
            // Ensure thread safety when accessing positions
            lock (_lockObject)
            {
                return _openPositions.Values
                    .Where(p => p.Category == Category.Linear && p.Quantity > 0) // Only consider open linear positions
                    .Sum(p => p.MaintenanceMargin ?? 0m); // Sum their MaintenanceMargin property
            }
        }

        #endregion // Margin and Liquidation Helpers

        /// <summary>
        /// Closes a position for the specified symbol
        /// </summary>
        /// <param name="symbol">The symbol to close</param>
        /// <returns>Result of the operation</returns>
        public override async Task<OperationResult> ClosePositionAsync(string symbol)
        {
            // This method is primarily for one-way mode (PositionMode.MergedSingle)
            if (_currentPositionMode == PositionMode.BothSides)
            {
                _log.Warning("ClosePositionAsync(symbol) called in Hedge Mode. Use ClosePositionAsync(symbol, direction) instead.");
                return new OperationResult { IsSuccess = false, Message = "Use ClosePositionAsync with direction in Hedge Mode." };
            }

            try
            {
                //await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false); // it is already inside below PlaceFuturesOrderAsync
                // In one-way mode, the key is just the symbol
                string positionKey = GetPositionKey(symbol, null);
                if (!_openPositions.TryGetValue(positionKey, out var position) || position.Category != Category.Linear)
                {
                    return new OperationResult
                    {
                        IsSuccess = false,
                        Message = $"No Linear position found for symbol {symbol} in one-way mode"
                    };
                }

                // Determine the order side to close the position
                var closingSide = position.Side == PositionSide.Buy ? OrderSide.Sell : OrderSide.Buy;

                // Place a market order to close the position
                var result = await PlaceFuturesOrderAsync(
                    symbol: symbol,
                    side: closingSide,
                    quantity: Math.Abs(position.Quantity),
                    price: null, // Market order
                    leverage: position.Leverage,
                    orderType: OrderType.Market,
                    positionDirection: position.Direction,
                    timeInForce: TimeInForce.GoodTillCancel,
                    reduceOnly: false);

                return new OperationResult
                {
                    IsSuccess = result.IsSuccess,
                    Message = result.IsSuccess ? "Position closed successfully" : $"Failed to close position: {result.Message}"
                };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    IsSuccess = false,
                    Message = $"Error closing position: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Closes a position for the specified symbol and direction
        /// </summary>
        /// <param name="symbol">The symbol to close</param>
        /// <param name="direction">The position direction to close</param>
        /// <returns>Result of the operation</returns>
        public override async Task<OperationResult> ClosePositionAsync(string symbol, PositionDirection? direction)
        {
            // This method handles both modes, but requires direction in Hedge Mode
            if (_currentPositionMode == PositionMode.BothSides && !direction.HasValue)
            {
                 _log.Error("ClosePositionAsync called without direction in Hedge Mode.");
                 return new OperationResult { IsSuccess = false, Message = "Direction is required in Hedge Mode." };
            }
            if (_currentPositionMode == PositionMode.MergedSingle && direction.HasValue)
            {
                 _log.Warning($"ClosePositionAsync called with direction '{direction}' in One-Way Mode. Direction will be ignored.");
                 direction = null; // Ensure direction is null for key generation in one-way
            }

            try
            {
                //await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false); // it is already inside below PlaceFuturesOrderAsync
                // Find the position using the appropriate key
                string positionKey = GetPositionKey(symbol, direction);
                if (!_openPositions.TryGetValue(positionKey, out var position) || position.Category != Category.Linear)
                {
                    return new OperationResult
                    {
                        IsSuccess = false,
                        Message = $"No Linear position found for symbol {symbol}" + (direction.HasValue ? $" with direction {direction}" : "")
                    };
                }

                // Determine the order side to close the position
                var closingSide = position.Side == PositionSide.Buy ? OrderSide.Sell : OrderSide.Buy;

                // Place a market order to close the position
                var result = await PlaceFuturesOrderAsync(
                    symbol: symbol,
                    side: closingSide,
                    quantity: Math.Abs(position.Quantity),
                    price: null, // Market order
                    leverage: position.Leverage,
                    orderType: OrderType.Market,
                    positionDirection: position.Direction,
                    timeInForce: TimeInForce.GoodTillCancel,
                    reduceOnly: false);

                return new OperationResult
                {
                    IsSuccess = result.IsSuccess,
                    Message = result.IsSuccess ? "Position closed successfully" : $"Failed to close position: {result.Message}"
                };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    IsSuccess = false,
                    Message = $"Error closing position: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Sets the position mode for futures trading in the simulation.
        /// Overrides the base implementation to add a check for open positions,
        /// mimicking stricter exchange behavior.
        /// </summary>
        /// <param name="mode">The position mode to set</param>
        /// <returns>Result of the operation</returns>
        public override async Task<OperationResult> SetPositionModeAsync(PositionMode mode)
        {
            try
            {
                await Task.Delay(_simConfig.ApiCallLatency).ConfigureAwait(false);
                // Check if there are any open Linear positions before allowing mode change.
                // This is a common restriction on real exchanges.
                bool hasOpenLinearPositions = _openPositions.Values.Any(p => p.Category == Category.Linear && Math.Abs(p.Quantity) > 0);

                if (hasOpenLinearPositions)
                {
                    int count = _openPositions.Values.Count(p => p.Category == Category.Linear && Math.Abs(p.Quantity) > 0);
                    string message = $"Cannot change position mode to {mode}. There are {count} open Linear positions.";
                    _log.Warning(message);
                    return new OperationResult
                    {
                        IsSuccess = false,
                        Message = message
                    };
                }

                // If no open positions, proceed with the base implementation to update the state.
                _log.Information($"Setting simulated position mode to {mode} (no open Linear positions found).");
                return await base.SetPositionModeAsync(mode); // Added await here
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error checking for open positions during SetPositionModeAsync");
                return new OperationResult
                {
                    IsSuccess = false,
                    Message = $"Internal error checking open positions: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Calculates the fee for a trade based on the configured fee rates
        /// </summary>
        private decimal CalculateFee(decimal tradeValue, bool isMaker, bool spot)
        {
            if (tradeValue <= 0)
                return 0;

            // Use the fee configuration from the base ExchangeConfig
            decimal feeRate = spot == true ? _config.Fees.GetSpotFee(isMaker) : _config.Fees.GetFuturesFee(isMaker);
            return tradeValue * feeRate;
        }

        /// <summary>
        /// Tracks a fee in the specified currency
        /// </summary>
        private void TrackFee(CoinType currency, decimal feeAmount)
        {
            if (feeAmount <= 0)
                return;

            // Deduct the fee from the wallet
            if (SimulatedWallet != null)
            {
                if (SimulatedWallet.DeductBalance(currency, feeAmount))
                {
                    _log.Information($"Fee charged: {feeAmount} {currency}");
                }
                else
                {
                     _log.Warning($"Failed to deduct fee {feeAmount} {currency} from wallet.");
                     // Continue tracking even if deduction fails, as the trade happened
                }
            }

            // Track the fee in the base class _sessionFees dictionary
            try
            {
                // Assume the fee is always in the quote currency for simplicity in simulation
                // A more complex simulation might handle base currency fees too.
                CurrencyPair pair = TradingPair; // Use the primary trading pair for the API instance
                CurrencyAmount feeCurrencyAmount = new CurrencyAmount(currency, feeAmount);
                Fee feeToAdd;

                if (currency == pair.QuoteCoin)
                {
                    feeToAdd = new Fee(new CurrencyAmount(pair.BaseCoin, 0), feeCurrencyAmount);
                }
                else if (currency == pair.BaseCoin)
                {
                    feeToAdd = new Fee(feeCurrencyAmount, new CurrencyAmount(pair.QuoteCoin, 0));
                }
                else
                {
                    _log.Warning($"Fee currency {currency} doesn't match TradingPair {pair}. Cannot track fee accurately.");
                    return;
                }

                 _sessionFees.AddOrUpdate(pair, feeToAdd, (key, existingFee) => existingFee + feeToAdd);
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Failed to update _sessionFees dictionary for currency {currency}");
            }
        }

        /// <summary>
        /// Gets the position key based on symbol and direction
        /// </summary>
        private string GetPositionKey(string symbol, PositionDirection? direction)
        {
            if (_currentPositionMode == PositionMode.BothSides && direction.HasValue)
            {
                // In hedge mode, we have separate positions for long and short
                return $"{symbol}_{direction.Value}";
            }
            else
            {
                // In one-way mode, we have a single position per symbol
                return symbol;
            }
        }

        // Helper to create OrderModelUpdate from OrderModel
        private OrderModelUpdate CreateOrderUpdateFromModel(OrderModel order)
        {
            // Use the extension method from Helpers.cs
            return order.ToOrderModelUpdate();
        }

        /// <summary>
        /// Helper method to check if a reduce-only order is valid against the current position state.
        /// </summary>
        /// <param name="symbol">Order symbol</param>
        /// <param name="orderSide">Side of the order (Buy/Sell)</param>
        /// <param name="positionDirection">Position direction specified for the order (relevant in Hedge Mode)</param>
        /// <param name="orderQuantity">Quantity of the order</param>
        /// <returns>True if the order can validly reduce a position, false otherwise.</returns>
        private bool CanReducePosition(string symbol, OrderSide orderSide, PositionDirection? positionDirection, decimal orderQuantity)
        {
            // Determine the position we expect to reduce based on the order's side and the account mode
            PositionDirection? directionToReduceKey = null; // Key used to look up position
            PositionSide sideToReduce; // The side the existing position must have

            if (_currentPositionMode == PositionMode.BothSides)
            {
                if (!positionDirection.HasValue)
                {
                    _log.Error("ReduceOnly check failed: PositionDirection missing in Hedge Mode for the order.");
                    return false; // Cannot determine which position to reduce
                }
                // In Hedge Mode, the order's positionDirection *is* the direction of the position it affects.
                directionToReduceKey = positionDirection.Value;
                // A Sell order reduces a Buy(Long) position. A Buy order reduces a Sell(Short) position.
                sideToReduce = (orderSide == OrderSide.Sell) ? PositionSide.Buy : PositionSide.Sell;
            }
            else // MergedSingle mode
            {
                // In one-way, a Sell order reduces a Buy side, and a Buy order reduces a Sell side.
                sideToReduce = (orderSide == OrderSide.Sell) ? PositionSide.Buy : PositionSide.Sell;
                directionToReduceKey = null; // Key uses null direction in one-way
            }

            string positionKey = GetPositionKey(symbol, directionToReduceKey);

            if (_openPositions.TryGetValue(positionKey, out var position))
            {
                // Check if the existing position's side matches the side we need to reduce
                if (position.Side == sideToReduce)
                {
                    // Position exists and is on the correct side.
                    if (position.Quantity > 0) // Ensure position has non-zero size
                    {
                        // Basic check passes: the position exists and is in the right direction.
                        // A more complex simulation *could* check if orderQuantity <= position.Quantity,
                        // but exchanges usually handle this by filling only up to the position size.
                        _log.Debug($"CanReducePosition check PASSED for order {orderSide} {orderQuantity} {symbol}. PositionKey: {positionKey}, PositionSide: {position.Side}, PositionQty: {position.Quantity}");
                        return true;
                    }
                    else
                    {
                        _log.Warning($"ReduceOnly check failed for key {positionKey}: Position exists but has zero quantity.");
                        return false;
                    }
                }
                else
                {
                    // This case implies trying to reduce in the wrong direction,
                    // e.g., a Sell reduceOnly order when the position for that key is already Short.
                    _log.Warning($"ReduceOnly check failed for key {positionKey}: Position exists but has wrong side ({position.Side}), expected side {sideToReduce} to reduce.");
                    return false;
                }
            }
            else
            {
                _log.Warning($"ReduceOnly check failed: No position found for key {positionKey}. Cannot reduce.");
                return false; // Position to reduce does not exist
            }
        }
    }
}

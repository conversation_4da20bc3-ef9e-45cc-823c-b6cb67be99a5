using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Models;
using MyTraderSpace.Config;
using MyTraderSpace.Logging;

namespace MyTraderSpace.UI
{
    public class ProgramExample
    {
        private static TextWriter _originalConsoleOut;
        private static TerminalConsoleInterface? _ui;
        private static ExchangeTrader? _trader;
        private static IUIDataProvider? _dataProvider;
        
        private static SimulationMode _uiSimMode = SimulationMode.Normal; // For testing purposes
        private static readonly CancellationTokenSource _programCts = new CancellationTokenSource();

        private static readonly LogManager _log = new LogManager("ProgramExample");

        public static async Task ExampleMain(string[] args)
        {
            _originalConsoleOut = Console.Out;
            
            try
            {
                _log.Information("Starting application example...");
                
                // Initialize Trader FIRST, as UI needs the data provider
                await InitializeTraderAsync();
                
                // Initialize UI AFTER trader, passing the data provider
                await InitializeUIAsync();
                
                // Start the trader's main execution loop (which includes strategy)
                // UI's internal loop is started by its StartUIThread in InitializeUIAsync
                if (_trader != null)
                {
                    await RunTraderAsync(); // Run the trader's main task
                }
                else
                {
                    _log.Error("Trader initialization failed, cannot run trader task.");
                    // Optionally wait for UI shutdown or exit immediately
                }
                
                // Wait for UI shutdown signal (UITask completes when Application.Run exits)
                if (_ui != null)
                {
                    await _ui.UITask;
                    _log.Information("UI Task completed.");
                }
                else
                {
                    _log.Warning("UI was not initialized, application exiting.");
                }
                
                _log.Information("Application example completed normally.");
            }
            catch (OperationCanceledException)
            {
                _log.Warning("Operation was cancelled.");
            }
            catch (Exception ex)
            {
                // Ensure we use the original console in case of critical errors
                if (Console.Out != _originalConsoleOut)
                {
                    Console.SetOut(_originalConsoleOut);
                }
                _log.Error(ex, "Critical application example error");
                Console.WriteLine($"Critical application error: {ex}"); // Also write direct
            }
            finally
            {
                // Perform cleanup in correct order
                await PerformNormalShutdownSequence();
            }
        }

        private static async Task InitializeTraderAsync()
        {
            _log.Information("Initializing ExchangeTrader (Example)...");

            try
            {
                // Example: Create necessary dependencies for the Example context
                ConfigurationLoader exampleConfigLoader = new ConfigurationLoader("config.json", "strategies.json");
                await exampleConfigLoader.LoadConfigAsync();
                string exampleModeName = exampleConfigLoader.GetAppConfig().DefaultMode; // Use default mode
                _log.Information($"Using mode: {exampleModeName}");

                // Initialize trader with the new constructor signature, passing the service
                _trader = new ExchangeTrader(exampleConfigLoader, exampleModeName);

                // Initialize the trader
                await _trader.InitializeAsync();
                _dataProvider = _trader.DataProvider; // Get the provider reference

                if (_dataProvider == null)
                {
                    throw new InvalidOperationException("Trader initialized but did not provide a DataProvider (MainStrategy might be null).");
                }

                _log.Information("ExchangeTrader initialized successfully (Example)");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error initializing trader (Example)");
                // Basic cleanup if trader init fails
                if (_trader != null)
                {
                    try { await _trader.StopAsync(); (_trader as IDisposable)?.Dispose(); } catch { }
                    _trader = null;
                    _dataProvider = null;
                }
                // Don't cancel CTS here, let the main loop handle the exception
                throw; // Re-throw to signal failure
            }
        }

        private static async Task InitializeUIAsync()
        {
            _log.Information("Initializing Terminal UI...");

            // Ensure data provider is available from trader init
            if (_dataProvider == null)
            {
                throw new InvalidOperationException("Cannot initialize UI: DataProvider is not available (Trader initialization likely failed).");
            }

            try
            {
                // Create the UI instance, passing the data provider
                // _uiSimMode is NOT passed to constructor anymore
                _ui = new TerminalConsoleInterface(_programCts.Token, _originalConsoleOut, _dataProvider, _uiSimMode);
                
                // Set up UI event handlers
                _ui.UIFailed += OnUIFailed;
                _ui.ShutdownRequested += OnShutdownRequested;
                _ui.CommandEntered += OnCommandEntered;
                
                // Initialize the UI
                await _ui.InitializeAsync();
                
                // Start the UI thread (which now includes the internal update loop)
                _ui.StartUIThread();
                
                // Wait for the console to be ready for redirection
                await _ui.WaitForConsoleReadyAsync();
                
                // Redirect console output to the UI
                Console.SetOut(_ui.Writer);
                
                // From this point, all Console.WriteLine goes to the UI
                _log.Information("UI initialized successfully, console output redirected");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Fatal error initializing UI");
                
                // Make sure we're writing to original console in case of errors
                if (Console.Out != _originalConsoleOut)
                    Console.SetOut(_originalConsoleOut);
                Console.WriteLine($"UI initialization failed: {ex}");
                
                // Cleanup any UI resources
                if (_ui != null)
                {
                    try
                    {
                        _ui.UIFailed -= OnUIFailed;
                        _ui.ShutdownRequested -= OnShutdownRequested;
                        _ui.CommandEntered -= OnCommandEntered;
                        await _ui.ShutdownAsync(); // Attempt graceful shutdown
                        _ui.Dispose();
                    }
                    catch (Exception cleanupEx)
                    {
                        Console.WriteLine($"Error during UI cleanup: {cleanupEx.Message}");
                    }
                }
                
                // Re-throw to prevent trader initialization
                throw;
            }
        }

        private static async Task RunTraderAsync()
        {
            if (_trader == null)
            {
                _log.Warning("RunTraderAsync called but trader is null.");
                return;
            }
            _log.Information("Starting ExchangeTrader main loop...");
            
            try
            {
                // Run the trader's main loop (this will run the MainStrategy.RunAsync)
                await _trader.RunAsync(); // Let it run until cancelled or completes
                _log.Information("ExchangeTrader main loop completed normally.");
            }
            catch (OperationCanceledException)
            {
                _log.Information("ExchangeTrader RunAsync was cancelled.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error in ExchangeTrader RunAsync");
                // Signal application to shutdown if trader fails critically
                if (!_programCts.IsCancellationRequested)
                    _programCts.Cancel();
                // Don't rethrow here, let the main loop handle completion/failure
            }
        }

        private static async Task PerformNormalShutdownSequence()
        {
            _log.Information("Performing shutdown sequence...");
            
            try
            {
                // Cancel program token if not already cancelled
                if (!_programCts.IsCancellationRequested)
                {
                    _log.Debug("Shutdown: Cancelling main CTS.");
                    _programCts.Cancel();
                }
                
                // Wait for Trader task with timeout
                if (_trader != null) // Check if trader was successfully initialized
                {
                    _log.Information("Shutdown: Waiting for Trader task...");
                    // TODO: Add a way to get the trader's RunAsync task if needed
                    // Assuming RunTraderAsync returns the task
                    // Task traderTask = ???; // We need a way to get the trader's RunAsync task if needed
                    // If RunTraderAsync was awaited in Main, we don't need to wait again here.
                    // We just need to ensure StopAsync is called.
                }
                
                // Shutdown trader first
                if (_trader != null)
                {
                    _log.Information("Shutting down ExchangeTrader...");
                    try
                    {
                        await _trader.StopAsync().WaitAsync(TimeSpan.FromSeconds(10)); // Add timeout
                        _log.Information("ExchangeTrader StopAsync complete or timed out.");
                        (_trader as IDisposable)?.Dispose(); // Dispose trader (handles service disposal)
                        _log.Information("ExchangeTrader disposed.");
                    }
                    catch (TimeoutException)
                    {
                        _log.Warning("Timeout during Trader StopAsync.");
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex, "Error shutting down trader.");
                    }
                    _trader = null;
                    _dataProvider = null;
                }
                
                // Shutdown UI last
                if (_ui != null)
                {
                    _log.Information("Shutting down UI...");
                    try
                    {
                        // Unsubscribe from events FIRST
                        try { _ui.UIFailed -= OnUIFailed; } catch {}
                        try { _ui.ShutdownRequested -= OnShutdownRequested; } catch {}
                        try { _ui.CommandEntered -= OnCommandEntered; } catch {}
                        
                        // Perform UI shutdown (which waits for its internal task)
                        await _ui.ShutdownAsync().WaitAsync(TimeSpan.FromSeconds(5)); // Add timeout
                        _log.Information("UI ShutdownAsync complete or timed out.");
                        _ui.Dispose();
                        _log.Information("UI disposed.");
                    }
                    catch (TimeoutException)
                    {
                        _log.Warning("Timeout during UI ShutdownAsync.");
                    }
                    catch (Exception ex)
                    {
                        // Log error to original console if possible
                        var console = Console.Out == _ui?.Writer ? _originalConsoleOut : Console.Out;
                        console.WriteLine($"Error shutting down UI: {ex.Message}");
                        _log.Error(ex, "Error shutting down UI.");
                    }
                    _ui = null;
                }
                
                // Restore original console if it wasn't already
                if (Console.Out != _originalConsoleOut)
                {
                    Console.SetOut(_originalConsoleOut);
                    _log.Information("Shutdown: Original console output restored.");
                }
                
                // Dispose the program CTS
                try { _programCts.Dispose(); } catch {}
                
                _log.Information("Application shutdown sequence complete.");
            }
            catch (Exception ex)
            {
                var console = Console.Out == _ui?.Writer ? _originalConsoleOut : Console.Out;
                console.WriteLine($"Error during shutdown sequence: {ex}");
                _log.Error(ex, "Error during shutdown sequence.");
            }
        }

        private static async void OnUIFailed(object? sender, Exception ex)
        {
            _log.Error(ex, "UI failure detected");
            
            // Ensure we're using the original console
            if (Console.Out != _originalConsoleOut)
                Console.SetOut(_originalConsoleOut);
            Console.WriteLine($"UI failed with error: {ex}");
            
            // Signal cancellation
            if (!_programCts.IsCancellationRequested)
                _programCts.Cancel();
            
            // Trigger shutdown sequence (don't await void method)
            await PerformNormalShutdownSequence(); // Or maybe just signal cancellation and let main finally handle it?
                                                   // Calling full sequence here might be complex if main is also trying.
                                                   // Sticking to just _programCts.Cancel() might be safer.
                                                   // Let's just cancel for now.
            // If cancellation doesn't lead to shutdown, reconsider calling PerformNormalShutdownSequence here.
        }

        private static void OnShutdownRequested(object? sender, EventArgs e)
        {
            _log.Information("Shutdown requested from UI");
            if (!_programCts.IsCancellationRequested)
                _programCts.Cancel();
        }

        private static void OnCommandEntered(object? sender, string command)
        {
            _log.Information($"Command entered: {command}");
            if (_programCts.IsCancellationRequested)
            {
                _log.Warning("Command ignored, shutdown in progress.");
                return;
            }

            try
            {
                // Process commands using trader instance
                if (_trader != null)
                {
                    ProcessTraderCommand(_trader, command);
                }
                else
                {
                    _log.Warning("Trader not available to process command");
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error processing command");
            }
        }

        private static void ProcessTraderCommand(ExchangeTrader trader, string command)
        {
            // Using DataProvider which comes from MainStrategy
            var snapshot = trader?.DataProvider?.GetUIDataSnapshot();

            // Handle basic commands
            switch (command.ToLower())
            {
                case "status":
                    _log.Information($"Trader Status: {trader?.TraderState}, Strategy: {snapshot?.StrategyStatus ?? "N/A"}, API: {snapshot?.APIStatus ?? ExchangeState.Stopped}");
                    break;
                    
                case "balance":
                    _log.Information($"Current Equity: {snapshot?.TotalEquity?.ToString("F2") ?? "N/A"}, U PnL: {snapshot?.UnrealizedPnL?.ToString("F2") ?? "N/A"}, R PnL: {snapshot?.RealizedPnL?.ToString("F2") ?? "N/A"}");
                    break;
                    
                case "help":
                    _log.Information("Available commands: status, balance, help, exit");
                    break;
                    
                case "exit":
                    OnShutdownRequested(null, EventArgs.Empty); // Trigger shutdown
                    break;
                    
                default:
                    _log.Warning($"Unknown command: {command}");
                    break;
            }
        }
    }
} 
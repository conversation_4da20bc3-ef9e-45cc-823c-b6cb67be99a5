﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MyTraderSpace.Config;
using MyTraderSpace.Models;
using MyTraderSpace.Logging;
using System.Collections.Concurrent;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Exchanges.Bybit;
using MyTraderSpace.Trading;
using MyTraderSpace.Trading.Strategies;

namespace MyTraderSpace
{
    public class ExchangeTrader : IDisposable
    {
        private readonly CancellationTokenSource _cts = new();
        private readonly LogManager _log;
        private readonly ConfigurationLoader _configLoader;
        private readonly string _modeName;

        public ExchangeState TraderState { get; private set; }

        private readonly List<BaseExchangeAPI> _spotPool = new();
        private readonly List<BaseExchangeAPI> _futuresPool = new();
        private IMarketDataService? _marketDataService;
        private BaseExchangeAPI? _selectedApiForConsole;

        // The main strategy orchestrator
        private MainStrategy? _mainStrategy;

        // Add a property to expose the MainStrategy as an IUIDataProvider
        public IUIDataProvider? DataProvider => _mainStrategy;

        public IEnumerable<BaseExchangeAPI> GetAllExchangeAPIs()
        {
            return _spotPool.Concat(_futuresPool).ToList();
        }

        public ExchangeTrader(ConfigurationLoader configLoader, string modeName)
        {
            _configLoader = configLoader ?? throw new ArgumentNullException(nameof(configLoader));
            _modeName = modeName ?? throw new ArgumentNullException(nameof(modeName));
            _log = new LogManager("ExchangeTrader");
            TraderState = ExchangeState.Initializing;

            _log.Information($"ExchangeTrader created for mode: {_modeName}.");
        }

        public async Task InitializeAsync()
        {
            TraderState = ExchangeState.Initializing;
            _log.Information($"Initializing ExchangeTrader for mode '{_modeName}'...");

            try
            {
                _log.Information("Creating Market Data Service...");
                _marketDataService = _configLoader.CreateMarketDataService(_modeName);
                await _marketDataService.InitializeAsync();
                _log.Information("Market Data Service Initialized.");

                _log.Information("Populating API pools from configuration...");
                AppConfig appConfig = _configLoader.GetAppConfig();
                if (!appConfig.Modes.TryGetValue(_modeName, out var modeConfig))
                {
                    throw new KeyNotFoundException($"Trading mode '{_modeName}' not found in configuration during Trader InitializeAsync.");
                }

                if (modeConfig.Exchanges == null || !modeConfig.Exchanges.Any())
                {
                    _log.Warning($"No exchanges configured for mode '{_modeName}'. API pools will be empty.");
                }
                else
                {
                    foreach (var exchangeEntry in modeConfig.Exchanges)
                    {
                        if (string.IsNullOrWhiteSpace(exchangeEntry.Name))
                        {
                            _log.Warning($"Skipping exchange entry of type '{exchangeEntry.Type}' because its 'Name' is missing in config for mode '{_modeName}'.");
                            continue;
                        }
                        try
                        {
                            _log.Information($"Creating API instance for '{exchangeEntry.Name}' (Type: {exchangeEntry.Type})...");
                            var api = _configLoader.CreateExchangeAPI(_modeName, exchangeEntry.Name, _marketDataService);

                            bool addedToPool = false;
                            if (exchangeEntry.Pools != null)
                            {
                                if (exchangeEntry.Pools.Contains("Futures", StringComparer.OrdinalIgnoreCase))
                                {
                                    AddToFuturesPool(api);
                                    addedToPool = true;
                                }
                                if (exchangeEntry.Pools.Contains("Spot", StringComparer.OrdinalIgnoreCase))
                                {
                                    AddToSpotPool(api);
                                    addedToPool = true;
                                }
                            }
                            if (!addedToPool)
                            {
                                _log.Warning($"Exchange entry '{exchangeEntry.Name}' has no recognized pools (Spot/Futures) defined. API not added to trader.");
                            }
                        }
                        catch (Exception apiEx)
                        {
                            _log.Error(apiEx, $"Failed to create or add API instance for config entry '{exchangeEntry.Name}'.");
                        }
                    }
                }
                _log.Information($"API Pool Population Complete. Spot: {_spotPool.Count}, Futures: {_futuresPool.Count}.");

                var initializationTasks = new List<Task>();
                if (!GetAllExchangeAPIs().Any())
                {
                     _log.Warning("InitializeAsync: No APIs found in pools to initialize.");
                }
                else
                {
                    _log.Information("Initializing APIs in pools...");
                    foreach (var api in GetAllExchangeAPIs())
                    {
                        _log.Information($"Initializing API in pool: {api.Name ?? api.GetType().Name}");
                        initializationTasks.Add(api.InitializeAsync());
                    }
                    await Task.WhenAll(initializationTasks);
                    _log.Information("All Exchange APIs in pools initialized successfully.");
                }

                _log.Information("Selecting primary API for strategy execution...");
                BaseExchangeAPI? primaryApi = GetFirstApi(); //_futuresPool.FirstOrDefault(api => api.Name == "Futures Demo 1");

                if (primaryApi == null)
                {
                    _log.Error("No suitable API found in pools (via GetFirstApi) to run the MainStrategy.");
                    throw new InvalidOperationException("Cannot initialize ExchangeTrader: No primary API available for the strategy.");
                }
                _log.Information($"Primary API selected for strategy: {primaryApi.Name ?? primaryApi.GetType().Name}");

                _selectedApiForConsole = primaryApi;
                _log.Information($"Default console API set to: {_selectedApiForConsole.Name ?? _selectedApiForConsole.GetType().Name}");

                _log.Information("Instantiating MainStrategy...");
                if (_marketDataService == null)
                {
                    throw new InvalidOperationException("MarketDataService was not initialized before MainStrategy instantiation.");
                }
                _mainStrategy = new MainStrategy(_configLoader, _marketDataService);

                // Set available APIs for MainStrategy before its initialization
                var allApis = GetAllExchangeAPIs();
                if (allApis.Any())
                {
                    _mainStrategy.SetAvailableApis(allApis);
                }
                else
                {
                    _log.Warning("No APIs available from ExchangeTrader to set for MainStrategy.");
                    // MainStrategy.InitializeAsync will handle the case where its own ApiPool remains empty.
                }

                await _mainStrategy.InitializeAsync();
                _log.Information("MainStrategy initialized.");

                TraderState = ExchangeState.Ready;
                _log.Information("ExchangeTrader initialized successfully.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Failed to initialize ExchangeTrader");
                TraderState = ExchangeState.Error;
                var disposableService = _marketDataService as IAsyncDisposable;
                if (disposableService != null)
                {
                    await disposableService.DisposeAsync();
                }
                else
                {
                    (_marketDataService as IDisposable)?.Dispose();
                }
                _marketDataService = null;
                await StopAsync();
                throw;
            }
        }

        public void AddToFuturesPool(BaseExchangeAPI exchangeAPI)
        {
            if (exchangeAPI == null)
                throw new ArgumentNullException(nameof(exchangeAPI));

            if (_futuresPool.Contains(exchangeAPI)) {
                 _log.Warning($"API {exchangeAPI.Name ?? exchangeAPI.GetType().Name} already exists in the futures pool.");
                 return;
            }
            _log.Information($"Adding {exchangeAPI.Name ?? exchangeAPI.GetType().Name} to futures pool");
            _futuresPool.Add(exchangeAPI);
        }

        public void AddToSpotPool(BaseExchangeAPI exchangeAPI)
        {
            if (exchangeAPI == null)
                throw new ArgumentNullException(nameof(exchangeAPI));

            if (_spotPool.Contains(exchangeAPI)) {
                 _log.Warning($"API {exchangeAPI.Name ?? exchangeAPI.GetType().Name} already exists in the spot pool.");
                 return;
            }
            _log.Information($"Adding {exchangeAPI.Name ?? exchangeAPI.GetType().Name} to spot pool");
            _spotPool.Add(exchangeAPI);
        }

        public async Task RunAsync()
        {
            if (TraderState == ExchangeState.Running)
            {
                _log.Warning("Called ExchangeTrader.RunAsync(): Trader is already Running");
                return;
            }

            if (TraderState != ExchangeState.Ready || _mainStrategy == null)
            {
                _log.Error($"ExchangeTrader must be in Ready state and MainStrategy initialized before calling RunAsync. State: {TraderState}, MainStrategy null: {_mainStrategy == null}");
                throw new InvalidOperationException("ExchangeTrader not ready or MainStrategy not initialized.");
            }

            TraderState = ExchangeState.Running;
            _log.Information("ExchangeTrader RunAsync starting...");

            Task strategyTask = Task.CompletedTask;
            Task periodicChecksTask = Task.CompletedTask;

            try
            {
                await _mainStrategy.StartAsync();

                _log.Information("Starting MainStrategy execution task and periodic checks task...");
                strategyTask = _mainStrategy.RunAsync(_cts.Token);
                periodicChecksTask = RunPeriodicChecksAsync();

                // Monitor both tasks concurrently
                while (!_cts.Token.IsCancellationRequested)
                {
                    // Wait for either task to complete or cancellation
                    var completedTask = await Task.WhenAny(strategyTask, periodicChecksTask);

                    if (_cts.Token.IsCancellationRequested)
                    {
                        _log.Information("Trader RunAsync cancellation requested during task wait.");
                        break; // Exit loop if cancelled while waiting
                    }

                    // --- Check which task completed ---
                    if (completedTask == strategyTask)
                    {
                        if (strategyTask.IsFaulted)
                        {
                            _log.Error(strategyTask.Exception?.Flatten(), "MainStrategy execution task failed. Stopping trader."); // Log full exception
                            RequestStop(); // Signal trader shutdown
                        }
                        else if (strategyTask.IsCompletedSuccessfully)
                        {
                            _log.Information("MainStrategy execution task completed normally. Stopping trader.");
                            RequestStop(); // Signal trader shutdown
                        }
                        // If cancelled, the main loop condition or outer catch will handle it.
                        break; // Strategy finished/failed, exit monitoring loop
                    }
                    else if (completedTask == periodicChecksTask)
                    {
                        if (periodicChecksTask.IsFaulted)
                        {
                            _log.Error(periodicChecksTask.Exception?.Flatten(), "Periodic checks task failed. This is critical. Stopping trader."); // Log full exception
                            RequestStop(); // Signal trader shutdown
                        }
                        else // Completed normally - unexpected for periodic checks
                        {
                            _log.Warning("Periodic checks task completed unexpectedly. Stopping trader.");
                            RequestStop(); // Signal trader shutdown
                        }
                        break; // Checks failed/finished, exit monitoring loop
                    }
                    else
                    {
                        // Should not happen with WhenAny unless cancellation occurs exactly between check and completion
                        _log.Warning("Task.WhenAny returned an unexpected task or null.");
                        await Task.Delay(100, _cts.Token); // Small delay before re-checking
                    }
                }
            }
            catch (OperationCanceledException) when (_cts.Token.IsCancellationRequested)
            {
                _log.Information("Trader RunAsync operation was cancelled (outer catch).");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Critical error in Trader RunAsync outer scope");
                TraderState = ExchangeState.Error;
                RequestStop(); // Ensure stop is requested on critical error
                // Don't re-throw here; Program.cs should await the trader task
            }
            finally
            {
                _log.Information("Trader RunAsync entering finally block. Waiting for tasks to complete...");

                // Ensure cancellation is signaled if loop exited for other reasons
                RequestStop();

                // Wait briefly for tasks to finish after cancellation
                try
                {
                    List<Task> tasksToAwait = new List<Task>();
                    if (strategyTask != null && !strategyTask.IsCompleted)
                        tasksToAwait.Add(strategyTask);
                    if (periodicChecksTask != null && !periodicChecksTask.IsCompleted)
                        tasksToAwait.Add(periodicChecksTask);

                    if (tasksToAwait.Count > 0)
                    {
                        // Use a separate timeout CancellationToken for waiting
                        using var cleanupTimeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                        var completedCleanupTask = await Task.WhenAny(Task.WhenAll(tasksToAwait), Task.Delay(Timeout.Infinite, cleanupTimeoutCts.Token));

                        if (completedCleanupTask != Task.WhenAll(tasksToAwait)) // Check if timeout occurred
                        {
                            _log.Warning("Timeout waiting for strategy/periodic tasks to complete during RunAsync cleanup.");
                        }
                        else
                        {
                            _log.Information("Strategy and periodic tasks completed during RunAsync cleanup.");
                        }
                    } else {
                        _log.Information("No running strategy or periodic tasks to await in RunAsync cleanup.");
                    }
                }
                catch (OperationCanceledException)
                {
                    _log.Warning("Operation cancelled while waiting for tasks during RunAsync cleanup.");
                }
                 catch (Exception ex)
                {
                    _log.Error(ex, "Exception during task awaiting in RunAsync finally block.");
                }


                // Update state if still running (means cancellation happened externally)
                if (TraderState == ExchangeState.Running)
                {
                    TraderState = ExchangeState.Stopping;
                }
                _log.Information($"Trader RunAsync finished. Final State in RunAsync: {TraderState}");
            }
        }

        private async Task RunPeriodicChecksAsync()
        {
            _log.Information("Starting Trader Periodic Checks Task ...");
            CheckStuffConfig? checkStuffConfig = null;
            try
            {
                // Get CheckStuffConfig from the loaded AppConfig
                checkStuffConfig = _configLoader.GetAppConfig()?.Parameters?.CheckStuffConfig;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Failed to retrieve CheckStuffConfig during periodic checks task startup.");
                // If config cannot be retrieved, checks depending on it should not run.
            }

            if (checkStuffConfig == null)
            {
                _log.Warning("CheckStuffConfig is null or couldn't be loaded. Periodic checks based on interval are disabled.");
                // Optionally perform checks that don't depend on the interval here, or just stop the task.
                // For now, we'll let the task run but the interval check will always fail.
                // Consider adding a check later: if (checkStuffConfig == null) { TraderState = ExchangeState.Warning; return; }
            }
            else
            {
                _log.Information($"Periodic checks interval set to {checkStuffConfig.IntervalMinutes} minutes.");
            }

            try
            {
                DateTime lastCheckTime = DateTime.MinValue;

                while (!_cts.Token.IsCancellationRequested)
                {
                    // Check if it's time to run maintenance using the retrieved config
                    if (checkStuffConfig != null &&
                        DateTime.UtcNow - lastCheckTime > TimeSpan.FromMinutes(checkStuffConfig.IntervalMinutes))
                    {
                        await CheckStuffPeriodicallyAsync();
                        lastCheckTime = DateTime.UtcNow;
                    }

                    await Task.Delay(1000, _cts.Token); // Check every second
                }
            }
            catch (OperationCanceledException) when (_cts.Token.IsCancellationRequested)
            {
                _log.Information("Trader Periodic Checks Task Cancelled");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error in periodic checks task");
                throw; // Rethrow to allow the main RunAsync loop to handle it
            }
            finally
            {
                _log.Information("Trader Periodic Checks Task stopped");
            }
        }

        private async Task CheckStuffPeriodicallyAsync()
        {
            _log.Debug("Performing Periodic Checks ...");

            try
            {
                // Check all exchange APIs for any required maintenance
                var checkTasks = new List<Task>();
                foreach (var api in GetAllExchangeAPIs())
                {
                    _log.Debug($"Refreshing wallet balance for {api.Name ?? api.GetType().Name}");
                    checkTasks.Add(api.RefreshWalletBalancesAsync());
                    // Any other periodic checks can be added here
                }
                await Task.WhenAll(checkTasks); // Run checks concurrently
                _log.Debug("Periodic Checks finished.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error during periodic checks");
                // Continue execution, don't throw from here
            }
        }

        public void RequestStop()
        {
            if (_cts != null && !_cts.IsCancellationRequested)
            {
                _log.Information("Trader.RequestStop(): Cancelling main CancellationTokenSource.");
                _cts.Cancel();
            }
        }

        public async Task StopAsync()
        {
            if (TraderState == ExchangeState.Stopping || TraderState == ExchangeState.Stopped)
            {
                _log.Information($"Trader.StopAsync(): Already stopping or stopped (State: {TraderState}).");
                return;
            }

            TraderState = ExchangeState.Stopping;
            _log.Information("Trader.StopAsync(): Stopping trader...");

            // 1. Stop the MainStrategy first
            if (_mainStrategy != null)
            {
                _log.Information("Trader.StopAsync(): Stopping MainStrategy...");
                try
                {
                    await _mainStrategy.StopAsync();
                    _log.Information("Trader.StopAsync(): MainStrategy stopped successfully.");
                }
                catch (Exception ex)
                {
                    _log.Error(ex, "Error stopping MainStrategy during Trader.StopAsync.");
                    // Continue shutdown even if strategy stop fails
                }
            }
            else
            {
                _log.Warning("Trader.StopAsync(): MainStrategy instance was null.");
            }

            // 2. Shut down Exchange APIs
            _log.Information("Trader.StopAsync(): Shutting down exchange APIs...");
            try
            {
                var stopTasks = new List<Task>();

                foreach (var api in GetAllExchangeAPIs())
                {
                    _log.Information($"Trader.StopAsync(): Requesting shutdown for {api.Name ?? api.GetType().Name}...");
                    stopTasks.Add(api.ShutdownAsync());
                }

                if (stopTasks.Any())
                {
                    using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                    await Task.WhenAll(stopTasks).WaitAsync(timeoutCts.Token);
                    _log.Information("Trader.StopAsync(): All exchange APIs shut down successfully.");
                } else {
                    _log.Information("Trader.StopAsync(): No exchange APIs to shut down.");
                }
            }
            catch (OperationCanceledException)
            {
                _log.Warning("Trader.StopAsync(): Timeout waiting for exchange APIs to shut down.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Trader.StopAsync(): Error during API shutdown");
            }
            finally
            {
                 // Dispose internal CTS if owned here - check ownership logic
                 // Since Program.cs cancels its CTS which is linked to the UI's CTS,
                 // and ExchangeTrader uses its own internal CTS, disposing it here is correct.
                 _cts?.Dispose();
                TraderState = ExchangeState.Stopped;
                _log.Information("Trader.StopAsync(): Trader stopped.");

                // Dispose MainStrategy (which will dispose TradingStateSavingService)
                if (_mainStrategy != null && _mainStrategy is IDisposable disposableStrategy)
                {
                    _log.Information("Disposing MainStrategy...");
                    disposableStrategy.Dispose();
                }
            }
        }

        public BaseExchangeAPI? GetFirstApi()
        {
            // Prioritize Futures, then Spot
            var api = _futuresPool.FirstOrDefault() ?? _spotPool.FirstOrDefault();
            if (api == null)
            {
                _log.Warning("GetFirstApi called but no API instances available in pools.");
                // Consider throwing if an API is strictly required here, or return null
                // throw new InvalidOperationException("No API instances available in the trader pools");
            }
            return api;
        }

        // --- IDisposable Implementation ---
        private bool disposedValue = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    _log.Information("Disposing ExchangeTrader...");
                    // Ensure StopAsync is called - consider async dispose pattern if blocking is an issue
                    if (TraderState != ExchangeState.Stopped && TraderState != ExchangeState.Stopping && TraderState != ExchangeState.Disposed)
                    {
                         _log.Warning($"Dispose called on trader in state {TraderState}. Attempting synchronous StopAsync.");
                         try
                         {
                            StopAsync().Wait(TimeSpan.FromSeconds(15)); // Add timeout to prevent deadlock
                         }
                         catch (Exception ex) {
                            _log.Error(ex, "Exception during StopAsync within Dispose.");
                         }
                    }

                    // Dispose managed state (managed objects).
                    _cts?.Dispose(); // Ensure CTS is disposed even if StopAsync failed/timed out

                    // Dispose Market Data Service
                    if (_marketDataService != null)
                    {
                        _log.Information("Disposing MarketDataService...");
                        // Prefer IAsyncDisposable if available, otherwise IDisposable
                        // Note: Calling async methods in Dispose can be tricky. If StopAsync guarantees
                        // service shutdown, synchronous Dispose might be safer here.
                        // However, if IAsyncDisposable is the primary pattern, handle it carefully.
                        // For now, we prioritize sync Dispose if available, assuming StopAsync handled async cleanup.
                        (_marketDataService as IDisposable)?.Dispose();
                        // If async disposal is *required* even here, a different pattern (AsyncDisposable) is needed for the class.
                        _log.Information("MarketDataService disposed (sync attempt).");
                        _marketDataService = null;
                    }

                    // Dispose APIs (assuming they are owned/created by trader or need explicit disposal)
                    // This might be redundant if StopAsync->ShutdownAsync handles disposal
                    foreach (var api in GetAllExchangeAPIs())
                    {
                        (api as IDisposable)?.Dispose();
                    }
                     _spotPool.Clear();
                     _futuresPool.Clear();

                    _mainStrategy = null;

                    _log.Information("ExchangeTrader disposed.");
                }

                // TODO: free unmanaged resources (unmanaged objects) and override finalizer
                // TODO: set large fields to null
                disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
        // --- End IDisposable ---
    }
}
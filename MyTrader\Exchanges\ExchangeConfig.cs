using MyTraderSpace.Logging;
using MyTraderSpace.Models;

namespace MyTraderSpace.Exchanges
{
    public class ExchangeConfig
    {
        public LogLevel LogLevel { get; set; } = LogLevel.Information;

        // Trading limits
        private decimal _leverage = 100m;
        public decimal Leverage
        {
            get => _leverage;
            set
            {
                if (value < 1 || value > MaxLeverage)
                    throw new ArgumentException($"Leverage must be between 1 and {MaxLeverage}");
                _leverage = value;
            }
        }
        public decimal MaxLeverage { get; set; } = 100m;

        // Fee configuration
        public FeeConfig Fees { get; set; } = new();
        public class FeeConfig
        {
            public decimal SpotMakerFee { get; set; } = 0.0006m;    // 0.06%
            public decimal SpotTakerFee { get; set; } = 0.001m;     // 0.10%
            public decimal FuturesMakerFee { get; set; } = 0.0004m; // 0.04%
            public decimal FuturesTakerFee { get; set; } = 0.0006m; // 0.06%

            public decimal GetSpotFee(bool isMaker) => isMaker ? SpotMakerFee : SpotTakerFee;
            public decimal GetFuturesFee(bool isMaker) => isMaker ? FuturesMakerFee : FuturesTakerFee;
        }

        // Account configuration settings
        public MarginMode MarginMode { get; set; } = MarginMode.RegularMargin;
        public TradeMode TradeMode { get; set; } = TradeMode.CrossMargin;
        public PositionMode PositionMode { get; set; } = PositionMode.BothSides;

        // Timeout configuration
        public TimeoutConfig Timeouts { get; set; } = new();
        public class TimeoutConfig
        {
            // Default timeout for waiting for order status changes (in milliseconds)
            public int OrderStatusWait { get; set; } = 10000;  // 10 seconds
            
            // Default timeout for API calls (in milliseconds)
            public int ApiCall { get; set; } = 30000;  // 30 seconds
            
            // Default timeout for verification attempts (in milliseconds)
            public int VerificationDelay { get; set; } = 500;  // 0.5 seconds
            
            // Maximum number of verification attempts
            public int MaxVerificationAttempts { get; set; } = 3;
        }

        // Order defaults
        public OrderConfig OrderDefaults { get; set; } = new();
        public class OrderConfig
        {
            public OrderType SpotOrderType { get; set; } = OrderType.Market;
            public TimeInForce SpotTimeInForce { get; set; } = TimeInForce.GoodTillCancel;
            public OrderType FuturesOrderType { get; set; } = OrderType.Market;
            public TimeInForce FuturesTimeInForce { get; set; } = TimeInForce.GoodTillCancel;
            public PositionMode DefaultPositionMode { get; set; } = PositionMode.BothSides;
        }
    }
} 
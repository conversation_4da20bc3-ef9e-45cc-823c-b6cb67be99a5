using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Concurrent;
using MyTraderSpace.Config;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;

namespace MyTraderSpace.Trading.Strategies
{
    /// <summary>
    /// Main strategy orchestrator.
    /// </summary>
    public class MainStrategy : IUIDataProvider, IDisposable
    {
        private HedgeGridStrategy? _currentStrategy; // a.k.a. "Frontier strategy", the one from currently processing market data updates
        private HedgeGridStrategy? _selectedStrategy; // an individual selected HGS sub-strategy to show informatoions about in the UI. This is selected by an ExchangeTrader-level command-line command (TODO)
        private readonly ConfigurationLoader _configLoader;
        private readonly LogManager _log;
        private readonly IMarketDataService _marketDataService; // Assuming one MDS for all APIs MainStrategy uses for now
        private List<BaseExchangeAPI> MasterApiPool { get; set; } = new List<BaseExchangeAPI>(); // Populated by ExchangeTrader via SetAvailableApis
        private Dictionary<string, BaseExchangeAPI> ApiPool { get; set; } // The selected to-be-available-for-new-steps APIs from the apis provided by SetAvailableApis
        private ConcurrentDictionary<string, HedgeGridStrategy> _strategyPool; // Active HGS instances

        private MainStrategyConfig? _mainStrategyConfig;
        private HedgeGridStrategyConfig? _hedgeGridStrategyConfig;

        private FuturesMarketData? _latestFuturesDataFromCurrentHgs;
        private decimal? _latestAvgBidAskPrice =>
            (_latestFuturesDataFromCurrentHgs?.HighestBid.HasValue == true && _latestFuturesDataFromCurrentHgs?.LowestAsk.HasValue == true)
                ? (_latestFuturesDataFromCurrentHgs.HighestBid.Value + _latestFuturesDataFromCurrentHgs.LowestAsk.Value) / 2m
                : null;
        private Task _mainStrategyProcessingTask = Task.CompletedTask; // Task for ProcessMarketUpdateForNewStepsAsync
        private CancellationTokenSource? _runCts; // For the main RunAsync lifetime
        private bool _disposed = false; // For IDisposable

        private const int MaxRecentErrors = 10;
        private readonly LimitedConcurrentQueue<string> _recentErrors = new LimitedConcurrentQueue<string>(MaxRecentErrors); // TODO: don't forget to subscribe/receive the errors from the OrderSides

        public StrategyState State { get; private set; } = StrategyState.Initializing;

        public MainStrategy(ConfigurationLoader configLoader, IMarketDataService marketDataService)
        {
            _configLoader = configLoader ?? throw new ArgumentNullException(nameof(configLoader));
            _log = new LogManager(nameof(MainStrategy));
            _selectedStrategy = _currentStrategy;
            _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));

            ApiPool = new Dictionary<string, BaseExchangeAPI>();
            _strategyPool = new ConcurrentDictionary<string, HedgeGridStrategy>();
            _log.Information("[CONSTRUCTOR] MainStrategy created.");
        }

        // This supposed to be called before MainStrategy.InitializeAsync() !
        public void SetAvailableApis(IEnumerable<BaseExchangeAPI> exchangeApisFromTrader)
        {
            if (State != StrategyState.Initializing)
            {
                _log.Warning($"[API SETUP] SetAvailableApis called but MainStrategy state is {State}. Cannot set APIs. Returning.");
                return;
            }
            MasterApiPool.Clear();
            if (exchangeApisFromTrader != null && exchangeApisFromTrader.Any())
            {
                _log.Information($"[API SETUP] Received {exchangeApisFromTrader.Count()} exchange APIs from trader. Setting up master API pool.");
                foreach (var api in exchangeApisFromTrader)
                {
                    // Ensure not to add duplicates to MasterApiPool if this method could be called multiple times with overlapping sets.
                    if (!MasterApiPool.Any(a => a.Name == api.Name))
                    {
                        MasterApiPool.Add(api);
                    }
                    else
                    {
                        _log.Warning($"[API SETUP] API instance '{api.Name}' already in MasterApiPool. Skipping duplicate.");
                    }
                }
            }
            else
            {
                _log.Warning("[API SETUP] No exchange APIs provided by trader. Master API pool will be empty.");
                // No exception here, InitializeAsync will handle if ApiPool remains empty
            }
            _log.Information($"[API SETUP] MainStrategy master API pool updated. Count: {MasterApiPool.Count}. Clear ApiPool before InitializeAsync reconstructs it based on config and MasterApiPool.");
            ApiPool.Clear(); // Clear the working ApiPool, it will be populated in InitializeAsync
        }

        public async Task InitializeAsync()
        {
            if (State != StrategyState.Initializing)
            {
                _log.Warning($"[INIT] MainStrategy InitializeAsync called but state is {State}. Returning.");
                return;
            }
            _log.Information("[INIT] Initializing MainStrategy...");
            try
            {
                _mainStrategyConfig = _configLoader.GetStrategyConfig<MainStrategyConfig>(nameof(MainStrategy));
                _hedgeGridStrategyConfig = _configLoader.GetStrategyConfig<HedgeGridStrategyConfig>(nameof(HedgeGridStrategy));
                _log.Information("[INIT] MainStrategyConfig and HedgeGridStrategyConfig loaded.");

                // Populate MainStrategy's internal ApiPool from MasterApiPool based on config
                ApiPool.Clear(); // Ensure it's empty before populating
                if (_mainStrategyConfig.ApiNamesToUse != null && _mainStrategyConfig.ApiNamesToUse.Any())
                {
                    _log.Information($"[INIT] MainStrategyConfig.ApiNamesToUse has {_mainStrategyConfig.ApiNamesToUse.Count} entries. Filtering MasterApiPool.");
                    foreach (var apiNameInConfig in _mainStrategyConfig.ApiNamesToUse)
                    {
                        var apiInstanceFromMaster = MasterApiPool.FirstOrDefault(a => a.Name == apiNameInConfig);
                        if (apiInstanceFromMaster != null)
                        {
                            if (ApiPool.TryAdd(apiInstanceFromMaster.Name, apiInstanceFromMaster))
                            {
                                _log.Information($"[INIT] API '{apiInstanceFromMaster.Name}' added to MainStrategy's operational ApiPool from MasterApiPool.");
                        }
                            else
                        {
                                _log.Warning($"[INIT] API '{apiInstanceFromMaster.Name}' was already in MainStrategy's operational ApiPool. This shouldn't happen if ApiPool was cleared.");
                            }
                        }
                        else
                        {
                            _log.Warning($"[INIT] MainStrategyConfig specified API '{apiNameInConfig}', but it was not found in the MasterApiPool (count: {MasterApiPool.Count}). Skipping.");
                        }
                    }
                }
                else
                {
                    _log.Information("[INIT] MainStrategyConfig.ApiNamesToUse is empty. Attempting to use all APIs from MasterApiPool.");
                    foreach (var apiFromMaster in MasterApiPool)
                    {
                        if (ApiPool.TryAdd(apiFromMaster.Name, apiFromMaster))
                        {
                            _log.Information($"[INIT] API '{apiFromMaster.Name}' added to MainStrategy's operational ApiPool from MasterApiPool.");
                        }
                        else
                        {
                            _log.Warning($"[INIT] API '{apiFromMaster.Name}' was already in MainStrategy's operational ApiPool during full copy. This shouldn't happen.");
                        }
                    }
                }

                if (ApiPool.Count == 0)
                {
                    _log.Error($"[INIT] MainStrategy's operational ApiPool is empty after config processing. No APIs available to run strategies. MasterApiPool count: {MasterApiPool.Count}");
                    this.State = StrategyState.Error;
                    return;
                }
                _log.Information($"[INIT] MainStrategy's operational ApiPool populated. Count: {ApiPool.Count}. Starting reconstruction loop.");

                // --- Reconstruction Loop ---
                // Iterate a temporary list of keys because we might modify ApiPool during iteration
                var apiKeysToProcess = ApiPool.Keys.ToList(); 
                foreach (var apiKey in apiKeysToProcess)
                {
                    if (!ApiPool.TryGetValue(apiKey, out var apiToReconstructWith))
                    {
                        _log.Warning($"[INIT] API Key '{apiKey}' was expected in ApiPool but not found during reconstruction. Skipping.");
                        continue;
                    }

                    // For reconstruction, IntendedPrice is 0 initially; HGS.InitializeAsync will derive it.
                    // isInitialBlankSlateStep is false because we are trying to reconstruct existing state.
                    string hgsNameForReconstruction = $"HGS_{apiToReconstructWith.Name}"; // Name doesn't include price yet
                    
                    HedgeGridStrategy? reconstructedHgs = await _tryInitializeAndAddHgsToPool(
                        hgsNameForReconstruction, 
                        0, // IntendedPrice is 0 for reconstruction, HGS will derive it
                        apiToReconstructWith, 
                        _marketDataService, 
                        isInitialBlankSlateStep: false,
                        isReconstruction: true // Signal that this API should be removed from ApiPool on success by the helper
                    );

                    if (reconstructedHgs != null)
                    {
                        // _tryInitializeAndAddHgsToPool already added to _strategyPool and removed apiToReconstructWith from ApiPool
                            if (_currentStrategy == null)
                            {
                            _currentStrategy = reconstructedHgs;
                            _selectedStrategy ??= reconstructedHgs;
                            _currentStrategy.OnMarketDataUpdate += OnMainStrategyFuturesMarketDataUpdate;
                            _log.Information($"[INIT] Current strategy set to '{_currentStrategy.NameId}' (Derived IntendedPrice: {reconstructedHgs.IntendedPrice}, API: {apiToReconstructWith.Name}). Subscribed to its market data.");
                        }
                        _log.Information($"[INIT] Successfully reconstructed and added HGS '{reconstructedHgs.NameId}' (API: {apiToReconstructWith.Name}, Derived IntendedPrice: {reconstructedHgs.IntendedPrice}). APIs in ApiPool: {ApiPool.Count}");
                        }
                        else
                        {
                        // Helper logged errors. API remains in ApiPool.
                        _log.Information($"[INIT] Failed to reconstruct HGS with API '{apiToReconstructWith.Name}'. API remains available in ApiPool.");
                    }
                }

                _log.Information($"[INIT] Reconstruction scan finished. Active strategies in _strategyPool: {_strategyPool.Count}. APIs available in ApiPool: {ApiPool.Count}.");

                if (_strategyPool.IsEmpty)
                {
                    _log.Information("[INIT] No active HedgeGridStrategy instances reconstructed. Attempting blank slate start.");
                    await HandleBlankSlateStartAsync();
                }

                UpdateMarketDataSubscription(); // a.k.a. "DetermineCurrentStrategy"

                this.State = StrategyState.Ready;
                _log.Information($"[INIT] MainStrategy InitializeAsync finished. State: {State}. Active HGS: {_strategyPool.Count}. Available APIs: {ApiPool.Count}.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "[INIT] Critical error during MainStrategy InitializeAsync.");
                this.State = StrategyState.Error;
                throw; 
            }
        }

        private async Task<HedgeGridStrategy?> _tryInitializeAndAddHgsToPool(string hgsNameId, decimal intendedPrice, BaseExchangeAPI apiForHgs, IMarketDataService mdService, bool isInitialBlankSlateStep, bool isReconstruction = false)
        {
            if (_hedgeGridStrategyConfig == null)
            {
                _log.Error($"[{nameof(MainStrategy)}] _hedgeGridStrategyConfig is null. Cannot create HGS '{hgsNameId}'.");
                return null;
            }

            _log.Information($"[{nameof(MainStrategy)}] Attempting to create HGS '{hgsNameId}' with API '{apiForHgs.Name}'. IntendedPrice: {intendedPrice}, IsInitial: {isInitialBlankSlateStep}, IsReconstruction: {isReconstruction}.");
            var hgs = new HedgeGridStrategy(hgsNameId, intendedPrice, apiForHgs, mdService, _hedgeGridStrategyConfig, isInitialBlankSlateStep);
            hgs.OnRemovalRequest += HandleStrategyRemovalRequest;
            hgs.OnBaseOrderPairFilled += HandleBaseOrderPairFilledAsync;
            hgs.OnTakeProfitOrderPairFilled += HandleTakeProfitOrderPairFilled;
            hgs.OnStrategyError += HandleHGSError;

            try
            {
                await hgs.InitializeAsync();

                // If reconstructing, HGS internal IntendedPrice might have been updated.
                // The HGS NameId should ideally reflect the final IntendedPrice for clarity if it's part of the naming convention for new steps.
                // For now, hgsNameId passed in is used. If hgs.IntendedPrice changed, the original hgsNameId (based on API name or initial price) is the key.
                // This is complex if names *must* include the final price and be unique.
                // Let's assume hgsNameId is the definitive key for _strategyPool for now.

                if (!hgs.IsValid) // IsValid checks State == Ready && (derived/given) IntendedPrice > 0
                {
                    var reason = $"HGS '{hgs.NameId}' not valid after InitializeAsync. State: {hgs.State}, IntendedPrice: {hgs.IntendedPrice}.";
                    if (isInitialBlankSlateStep && hgs.IntendedPrice <= 0)
                    {
                        _log.Warning($"[{nameof(MainStrategy)}] {reason} This was an initial blank slate step; it's not an error yet, HGS awaits MainStrategy action.");
                        // For initial blank slate, we don't immediately throw or cleanup. MainStrategy.CreateAndActivate... will decide.
                        // However, this helper should probably signal failure if it's not going to be added to the pool *by this helper*.
                        // Let's return null and let the caller (CreateAndActivateNewHedgeGridStepAsync) handle API release for this specific case.
                        // For reconstruction, if not valid, it is an error.
                        if (!isReconstruction) return hgs; // Return the HGS, even if not "trade-ready", for the caller to manage specifically for blank slate.
                    }
                    throw new InvalidOperationException(reason);
                }

                // If HGS is valid (Ready and has IntendedPrice), try adding to pool
                if (!_strategyPool.TryAdd(hgs.NameId, hgs)) // Using hgs.NameId which might be just API based or price based from caller
                {
                    throw new InvalidOperationException($"Failed to add HGS '{hgs.NameId}' to _strategyPool (duplicate key?).");
                }

                // If successfully added to _strategyPool, remove the API from the available ApiPool
                // This applies if we are not just reconstructing (where API is already from ApiPool and should be removed)
                // or if it's a new step (where API was acquired by caller).
                // The parameter `isReconstruction` helps differentiate.
                // If `isReconstruction` is true, we expect `apiForHgs.Name` to be in `ApiPool`.
                // If it's for a new step, `apiForHgs` was given by `AcquireNextAvailableApi` and is NOT in `ApiPool` anymore.
                
                bool removedFromPool = false;
                if (isReconstruction)
                { // Only remove from ApiPool if we were processing an API from it.
                    removedFromPool = ApiPool.Remove(apiForHgs.Name);
                    if (!removedFromPool)
                    {
                        _log.Warning($"[{nameof(MainStrategy)}] HGS '{hgs.NameId}' (reconstruction) used API '{apiForHgs.Name}', but API was not found in ApiPool to remove. Potential issue.");
                    }
                }
                // If !isReconstruction, API was acquired by caller (e.g. CreateAndActivate...) and is already out of ApiPool.

                _log.Information($"[{nameof(MainStrategy)}] HGS '{hgs.NameId}' (IntendedPrice: {hgs.IntendedPrice}) initialized and added to _strategyPool. API '{apiForHgs.Name}' {(isReconstruction ? (removedFromPool ? "removed from" : "not found in") : "was acquired from")} ApiPool.");
                
                // Start the HGS if it's not just for reconstruction (where StartAsync is handled by main loop or explicit call)
                // Actually, StartAsync should generally be called by the main logic that decides an HGS is truly active.
                // Let's not call StartAsync from this helper. The caller should do it.
                // await hgs.StartAsync(); 
                // if (hgs.State != StrategyState.Running && hgs.State != StrategyState.Ready) { ... }

                return hgs;
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[{nameof(MainStrategy)}] Error in _tryInitializeAndAddHgsToPool for HGS '{hgs.NameId}'.");
                // Cleanup: remove from _strategyPool if added, unsubscribe, dispose
                _strategyPool.TryRemove(hgs.NameId, out _);
                hgs.OnRemovalRequest -= HandleStrategyRemovalRequest;
                hgs.OnBaseOrderPairFilled -= HandleBaseOrderPairFilledAsync;
                hgs.OnTakeProfitOrderPairFilled -= HandleTakeProfitOrderPairFilled;
                hgs.OnStrategyError -= HandleHGSError;
                (hgs as IDisposable)?.Dispose();
                // Do NOT release the API here. The caller (InitializeAsync reconstruction loop or CreateAndActivateNewHedgeGridStepAsync) is responsible for API release if this helper fails.
                return null;
            }
        }

        private async Task HandleBlankSlateStartAsync()
        {
            if (_mainStrategyConfig == null || _hedgeGridStrategyConfig == null)
            {
                 _log.Error("[BLANK SLATE] Configs not loaded. Cannot start.");
                return;
            }
            if (!_strategyPool.IsEmpty)
            {
                _log.Information("[BLANK SLATE] Strategy pool is not empty. Blank slate start not needed.");
                return;
            }

            BaseExchangeAPI? apiToUseForFirstStep = AcquireNextAvailableApi(); // Removes from ApiPool if successful
            if (apiToUseForFirstStep == null)
            {
                _log.Error("[BLANK SLATE] No available APIs in the pool to start the first step.");
                return;
            }

            // Ensure _latestFuturesDataFromCurrentHgs is available for price determination
            if (_latestFuturesDataFromCurrentHgs == null && _marketDataService != null)
            {
                _latestFuturesDataFromCurrentHgs = _marketDataService.GetLatestData().Futures;
            }

            decimal initialIntendedPrice = decimal.Zero;
            if (_latestAvgBidAskPrice != null && _latestAvgBidAskPrice > 0)
            {
                initialIntendedPrice = _latestAvgBidAskPrice.Value;
            }
            else if (_latestFuturesDataFromCurrentHgs != null) 
            {
                initialIntendedPrice = _latestFuturesDataFromCurrentHgs.MarkPrice ?? _latestFuturesDataFromCurrentHgs.LastPrice ?? 0m;
            }

            if (initialIntendedPrice <= 0)
            {
                _log.Error($"[BLANK SLATE] Could not determine a valid initial market price (resulted in {initialIntendedPrice}). Aborting blank slate start. Releasing API '{apiToUseForFirstStep.Name}'.");
                ReleaseApiToPool(apiToUseForFirstStep); // Release API if price determination fails
                return;
            }
            _log.Information($"[BLANK SLATE] Determined initial IntendedPrice: {initialIntendedPrice}. Using API '{apiToUseForFirstStep.Name}'.");

            // For blank slate, HGS name should include the determined price.
            string hgsNameForBlankSlate = $"HGS_{apiToUseForFirstStep.Name}_{initialIntendedPrice:F0}".Replace(".", "-");

            HedgeGridStrategy? newHgs = await _tryInitializeAndAddHgsToPool(
                hgsNameForBlankSlate,
                initialIntendedPrice,
                apiToUseForFirstStep,
                _marketDataService,
                isInitialBlankSlateStep: true,
                isReconstruction: false // This API was acquired, not from iterating ApiPool
            );

            if (newHgs != null && newHgs.IsValid)
            {
                // Helper already added to _strategyPool. API is considered used.
                _log.Information($"[BLANK SLATE] HGS '{newHgs.NameId}' successfully initialized for blank slate. Attempting to start.");
                await newHgs.StartAsync();
                if (newHgs.State != StrategyState.Running && newHgs.State != StrategyState.Ready)
                {
                    _log.Warning($"[BLANK SLATE] HGS '{newHgs.NameId}' did not become Running/Ready after StartAsync. State: {newHgs.State}. Removing and releasing API.");
                    _strategyPool.TryRemove(newHgs.NameId, out _); // Remove from pool
                    ReleaseApiToPool(apiToUseForFirstStep); // Release API
                    // Manually call cleanup for HGS events as helper's catch might not run if IsValid was true initially
                    newHgs.OnRemovalRequest -= HandleStrategyRemovalRequest;
                    newHgs.OnBaseOrderPairFilled -= HandleBaseOrderPairFilledAsync;
                    newHgs.OnTakeProfitOrderPairFilled -= HandleTakeProfitOrderPairFilled;
                    newHgs.OnStrategyError -= HandleHGSError;
                    (newHgs as IDisposable)?.Dispose();
                }
                else
                {
                    if (_currentStrategy == null) // Should be the case for a true blank slate
                    {
                        _currentStrategy = newHgs;
                        _selectedStrategy ??= newHgs;
                        _currentStrategy.OnMarketDataUpdate += OnMainStrategyFuturesMarketDataUpdate;
                        _log.Information($"[BLANK SLATE] Current strategy set to '{_currentStrategy.NameId}'. Subscribed to its market data.");
                    }
                    UpdateMarketDataSubscription(); // Ensure correct subscription
                }
            }
            else
            {
                _log.Error($"[BLANK SLATE] Failed to initialize HGS for blank slate with API '{apiToUseForFirstStep.Name}'. Releasing API.");
                ReleaseApiToPool(apiToUseForFirstStep); // Release API if HGS creation/init failed
                if (newHgs != null) // If HGS was created but not valid (e.g. initial step awaiting price)
                {
                    // Cleanup events for the non-valid HGS that wasn't added to the pool by the helper
                    newHgs.OnRemovalRequest -= HandleStrategyRemovalRequest;
                    newHgs.OnBaseOrderPairFilled -= HandleBaseOrderPairFilledAsync;
                    newHgs.OnTakeProfitOrderPairFilled -= HandleTakeProfitOrderPairFilled;
                    newHgs.OnStrategyError -= HandleHGSError;
                    (newHgs as IDisposable)?.Dispose();
                }
            }
        }

        private async Task CreateAndActivateNewHedgeGridStepAsync(decimal intendedPrice, BaseExchangeAPI apiToUse, IMarketDataService mdService, bool isInitialBlankSlateStep = false)
        {
            if (_mainStrategyConfig == null || _hedgeGridStrategyConfig == null)
            {
                _log.Error($"[{nameof(MainStrategy)}] Configs not loaded. Cannot create new step. API '{apiToUse.Name}' will be released.");
                ReleaseApiToPool(apiToUse);
                return;
            }

            if (_mainStrategyConfig.MaxActiveStrategies > 0 && _strategyPool.Count >= _mainStrategyConfig.MaxActiveStrategies)
            {
                _log.Information($"[NEW STEP] MaxActiveStrategies ({_mainStrategyConfig.MaxActiveStrategies}) reached. Cannot create for IntendedPrice {intendedPrice}. API '{apiToUse.Name}' released.");
                ReleaseApiToPool(apiToUse);
                return;
            }

            string hgsName = $"HGS_{apiToUse.Name}_{intendedPrice:F0}".Replace(".", "-"); // TODO / NOTE / QUESTION: Do we use price in name or not?
            if (_strategyPool.ContainsKey(hgsName))
            {
                _log.Warning($"[{nameof(MainStrategy)}] HGS '{hgsName}' already exists. API '{apiToUse.Name}' released.");
                ReleaseApiToPool(apiToUse);
                return;
            }

            HedgeGridStrategy? newHgs = await _tryInitializeAndAddHgsToPool(
                hgsName,
                intendedPrice,
                apiToUse,
                mdService,
                isInitialBlankSlateStep,
                isReconstruction: false // API was acquired by caller
            );

            if (newHgs != null && newHgs.IsValid)
            {
                // Helper added to _strategyPool. API is used.
                _log.Information($"[{nameof(MainStrategy)}] New HGS '{newHgs.NameId}' added. Attempting to start.");
                await newHgs.StartAsync();
                if (newHgs.State != StrategyState.Running && newHgs.State != StrategyState.Ready)
                {
                    _log.Warning($"[{nameof(MainStrategy)}] HGS '{newHgs.NameId}' did not become Running/Ready after StartAsync. State: {newHgs.State}. Removing and releasing API.");
                    _strategyPool.TryRemove(newHgs.NameId, out _);
                ReleaseApiToPool(apiToUse);
                    // Manually call cleanup for HGS events
                newHgs.OnRemovalRequest -= HandleStrategyRemovalRequest;
                newHgs.OnBaseOrderPairFilled -= HandleBaseOrderPairFilledAsync;
                newHgs.OnTakeProfitOrderPairFilled -= HandleTakeProfitOrderPairFilled;
                newHgs.OnStrategyError -= HandleHGSError;
                (newHgs as IDisposable)?.Dispose();
            }
                else
            {
                    UpdateMarketDataSubscription(); 
                }
            }
            else
            {
                _log.Error($"[{nameof(MainStrategy)}] Failed to initialize HGS '{hgsName}' for new step. API '{apiToUse.Name}' released.");
                ReleaseApiToPool(apiToUse); 
                if (newHgs != null) // HGS was created but not valid (e.g. initial step awaiting price)
                {
                     // Cleanup events for the non-valid HGS
                newHgs.OnRemovalRequest -= HandleStrategyRemovalRequest;
                newHgs.OnBaseOrderPairFilled -= HandleBaseOrderPairFilledAsync;
                newHgs.OnTakeProfitOrderPairFilled -= HandleTakeProfitOrderPairFilled;
                newHgs.OnStrategyError -= HandleHGSError;
                (newHgs as IDisposable)?.Dispose();
                }
            }
        }

        private async void HandleBaseOrderPairFilledAsync(HedgeGridStrategy strategy, OrderPair filledPair)
        {
            _log.Information($"[{nameof(MainStrategy)}] Received OnBaseOrderPairFilled from HGS '{strategy.NameId}' for pair '{filledPair.Name}'. LongFilled: {strategy.LongSide.IsBaseOrderFilled}, ShortFilled: {strategy.ShortSide.IsBaseOrderFilled}.");

            if (strategy.LongSide.IsBaseOrderFilled && strategy.ShortSide.IsBaseOrderFilled)
            {
                _log.Information($"[{nameof(MainStrategy)}] Both base orders for HGS '{strategy.NameId}' (IntendedPrice: {strategy.IntendedPrice}) are now filled.");

                // 1. Prune distant strategies
                // Pruning should happen relative to the strategy that just became fully active.
                // Prune strategies that are NOT 'strategy' AND are not IsActive() AND are too far.
                // Correction: Any strategy that has no base orders filled(no positions open) and no TP orders filled (IsActive() == false) could be considered as removable. Placed pending orders doesn't count
                // The reason for this is, that never in a liftime of a strategy will ever be again blank/empty/IsActive() == false, a BaseOrder or a TakeProfit order will be always placed
                await PruneDistantStrategiesAsync(strategy);

                // 2. Create two new HedgeGridStrategy instances (for new steps)
                if (_hedgeGridStrategyConfig == null)
                {
                    _log.Error($"[{nameof(MainStrategy)}] HedgeGridStrategyConfig is null. Cannot create new steps around HGS '{strategy.NameId}'.");
                    return;
                }
                decimal stepSize = _hedgeGridStrategyConfig.StepSize;
                decimal priceUp = strategy.IntendedPrice + stepSize;
                decimal priceDown = strategy.IntendedPrice - stepSize;

                _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' fully filled. Attempting to create new steps at PriceUp: {priceUp} and PriceDown: {priceDown}.");

                BaseExchangeAPI? apiForUpStep = AcquireNextAvailableApi();
                if (apiForUpStep != null)
                {
                    _log.Information($"[{nameof(MainStrategy)}] Acquired API '{apiForUpStep.Name}' for UpStep at {priceUp}.");
                    await CreateAndActivateNewHedgeGridStepAsync(priceUp, apiForUpStep, strategy.MarketDataService, false);
            }
            else
            {
                    _log.Warning($"[{nameof(MainStrategy)}] No available API for UpStep at {priceUp}.");
                }

                BaseExchangeAPI? apiForDownStep = AcquireNextAvailableApi();
                if (apiForDownStep != null)
                {
                    _log.Information($"[{nameof(MainStrategy)}] Acquired API '{apiForDownStep.Name}' for DownStep at {priceDown}.");
                    await CreateAndActivateNewHedgeGridStepAsync(priceDown, apiForDownStep, strategy.MarketDataService, false);
                }
                else
                {
                    _log.Warning($"[{nameof(MainStrategy)}] No available API for DownStep at {priceDown}.");
                }
            }
        }

        private void HandleTakeProfitOrderPairFilled(HedgeGridStrategy strategy, OrderPair pairThatTookProfit)
        {
            _log.Information($"[{nameof(MainStrategy)}] Received OnTakeProfitOrderPairFilled from HGS '{strategy.NameId}' for pair '{pairThatTookProfit.Name}'. PnL: {pairThatTookProfit.CalculatedRealizedPnL}.");
            // MainStrategy's primary role here is to be aware of the TP.
            // The HGS/OrderPair itself handles re-opening its base order.
            // MainStrategy might decide to create new steps if this TP fill moves the "center" of the grid significantly,
            // similar to the logic when a new HGS base orders fill. This requires more thought on how to define "significant move".
            // For now, let's log and potentially re-evaluate active strategy for market data.
            UpdateMarketDataSubscription(); 
            // TODO: Consider if new steps should be triggered here based on overall grid structure and current price.
            // This would be similar to HandleBaseOrderPairFilledAsync logic but triggered by TP.
            // Perhaps after a TP, we check if the current price is now > StepSize away from the *nearest active HGS*.
        }

        private void UpdateMarketDataSubscription()
        {
            if (_strategyPool.IsEmpty)
            {
                if (_currentStrategy != null)
                {
                    _currentStrategy.OnMarketDataUpdate -= OnMainStrategyFuturesMarketDataUpdate;
                    _log.Information($"[DATA SUB] Strategy pool empty. Unsubscribed from '{_currentStrategy.NameId}'.");
                    _currentStrategy = null;
                }
                return;
            }
            decimal referencePrice = _latestFuturesDataFromCurrentHgs?.MarkPrice ?? _strategyPool.Values.FirstOrDefault()?.IntendedPrice ?? 0;
            var newCurrentStrategy = _strategyPool.Values
                .Where(s => s.State == StrategyState.Running || s.State == StrategyState.Ready)
                .OrderBy(s => Math.Abs(s.IntendedPrice - referencePrice))
                .FirstOrDefault();

            if (newCurrentStrategy != null && newCurrentStrategy != _currentStrategy)
            {
                if (_currentStrategy != null)
                {
                    _currentStrategy.OnMarketDataUpdate -= OnMainStrategyFuturesMarketDataUpdate;
                    _log.Information($"[DATA SUB] Unsubscribed from market data of old current strategy '{_currentStrategy.NameId}'.");
                }
                _currentStrategy = newCurrentStrategy;
                _currentStrategy.OnMarketDataUpdate += OnMainStrategyFuturesMarketDataUpdate;
                _log.Information($"[DATA SUB] Subscribed to market data of new current strategy '{_currentStrategy.NameId}' (IntendedPrice: {_currentStrategy.IntendedPrice}). Reference Price: {referencePrice}");
            }
            else if (newCurrentStrategy == null && _currentStrategy != null) // No suitable running/ready strategy found
            {
                _currentStrategy.OnMarketDataUpdate -= OnMainStrategyFuturesMarketDataUpdate;
                _log.Warning($"[DATA SUB] No suitable strategy to subscribe to. Unsubscribed from '{_currentStrategy.NameId}'.");
                _currentStrategy = null;
            }
            else if (newCurrentStrategy != null && _currentStrategy == null) // First time selection
            {
                _currentStrategy = newCurrentStrategy;
                _currentStrategy.OnMarketDataUpdate += OnMainStrategyFuturesMarketDataUpdate;
                _log.Information($"[DATA SUB] Initial subscription to market data of current strategy '{_currentStrategy.NameId}' (IntendedPrice: {_currentStrategy.IntendedPrice}). Reference Price: {referencePrice}");
            }
        }

        private void OnMainStrategyFuturesMarketDataUpdate(FuturesMarketData data)
        {
            if (this.State != StrategyState.Running || data == null)
                return;
            _latestFuturesDataFromCurrentHgs = data;
            if (_mainStrategyProcessingTask.IsCompleted)
            {
                _mainStrategyProcessingTask = Task.Run(async () => await ProcessMarketUpdateForNewStepsAsync(data));
            }
        }

        private async Task ProcessMarketUpdateForNewStepsAsync(FuturesMarketData data)
        {
            // This method's original intent was to actively scan for gaps based on market price.
            // However, with HGSs creating new steps upon their own fills or TP fills (potentially),
            // this active scanning might be redundant or conflict.
            // For now, let's leave it empty or for very minimal checks not covered by event-driven step creation.
            // _log.Verbose($"[{nameof(MainStrategy)}] ProcessMarketUpdateForNewStepsAsync called with MarkPrice: {data.MarkPrice}");
            await Task.CompletedTask;
        }

        private async Task PruneDistantStrategiesAsync(HedgeGridStrategy currentActiveStrategy)
        {
            if (_hedgeGridStrategyConfig == null || _strategyPool.IsEmpty || currentActiveStrategy == null)
                return;

            _log.Information($"[{nameof(MainStrategy)}] Pruning distant strategies relative to active HGS '{currentActiveStrategy.NameId}' (IntendedPrice: {currentActiveStrategy.IntendedPrice}).");

            decimal referencePrice = currentActiveStrategy.IntendedPrice; // Use the newly active strategy's price
            decimal stepSize = _hedgeGridStrategyConfig.StepSize;
            // A strategy is distant if its IntendedPrice is more than one StepSize away from the reference.
            // (e.g., if current is 100, step 50, then anything < 0 or > 200 is too far if only 0, 50, 100, 150, 200 are target steps)
            // A simpler check: if a strategy's intended price is further than StepSize from the referencePrice,
            // and it's not the currentActiveStrategy itself, and it's not "active" (meaning its base orders are not filled).
            decimal maxAllowedDistance = stepSize; 

            var strategiesToPrune = new List<HedgeGridStrategy>();

            foreach (var stratInPool in _strategyPool.Values.ToList()) // ToList for safe iteration while potentially modifying
            {
                if (stratInPool.NameId == currentActiveStrategy.NameId)
                    continue; // Don't prune the current active one

                // Check if the stratInPool is "active" (e.g., has its base orders filled)
                // HGS.IsActive() is a potentially blocking call if it fetches positions.
                // Better to check HGS's internal state if possible, or rely on its reported State.
                // For now, let's assume a strategy that isn't Running isn't fully active for pruning consideration.
                // Or, more directly, if its base orders aren't filled.
                bool canBePruned = !(stratInPool.LongSide.IsBaseOrderFilled || stratInPool.ShortSide.IsBaseOrderFilled);

                if (canBePruned)
                {
                    decimal distance = Math.Abs(stratInPool.IntendedPrice - referencePrice);
                    // If a strategy is more than one step size away from the current active strategy, it's a candidate for pruning.
                    // Example: Active is 100, StepSize 50.
                    // A strategy at 0 (distance 100) or 200 (distance 100) should be pruned if they represent unfilled steps.
                    // A strategy at 50 (distance 50) or 150 (distance 50) should NOT be pruned by this logic.
                    // So, prune if distance > maxAllowedDistance (which is stepSize).
                    if (distance > maxAllowedDistance)
                    {
                        _log.Information($"[PRUNE] HGS '{stratInPool.NameId}' (IntendedPrice: {stratInPool.IntendedPrice}) is non-active and distant (Dist: {distance} > MaxAllowed: {maxAllowedDistance}) from active HGS '{currentActiveStrategy.NameId}'. Marking for removal.");
                        strategiesToPrune.Add(stratInPool);
                    }
                }
            }

            foreach (var stratToPrune in strategiesToPrune)
            {
                _log.Information($"[PRUNE] Requesting consolidation and removal for distant HGS '{stratToPrune.NameId}'.");
                await stratToPrune.ConsolidateAsync(); // Attempt to cancel orders/close positions
                // HandleStrategyRemovalRequest will be called by HGS.Consolidate or its own OnRemovalRequest if it decides to self-remove.
                // If ConsolidateAsync doesn't trigger removal, we might need to call it explicitly.
                // For now, assume HGS handles its removal request or Consolidate is sufficient before direct removal.
                HandleStrategyRemovalRequest(stratToPrune); // Explicitly remove after consolidation attempt
            }
        }

        private BaseExchangeAPI? AcquireNextAvailableApi(string? specificApiName = null)
        {
            BaseExchangeAPI? apiToReturn = null;
            if (!string.IsNullOrEmpty(specificApiName))
            {
                if (ApiPool.TryGetValue(specificApiName, out var apiInstance))
                {
                    apiToReturn = apiInstance;
                    ApiPool.Remove(apiInstance.Name);
                    _log.Information($"[API POOL] Acquired specific API '{specificApiName}'. Remaining available: {ApiPool.Count}");
                }
                else
                    _log.Warning($"[API POOL] Specific API '{specificApiName}' requested but not found in available pool.");
            }
            else if (ApiPool.Count != 0)
            {
                apiToReturn = ApiPool.First().Value;
                ApiPool.Remove(apiToReturn.Name);
                _log.Information($"[API POOL] Acquired next available API '{apiToReturn.Name}'. Remaining available: {ApiPool.Count}");
            }
            else 
                _log.Warning("[API POOL] No APIs available in the pool to acquire.");
            return apiToReturn;
        }

        private bool ReleaseApiToPool(BaseExchangeAPI? api)
        {
            if (api == null)
            {
                _log.Warning("[API POOL] Attempted to release a null API.");
                return false;
            }
            // Ensure it's an API managed by this MainStrategy instance and not already in the available list
            if (!ApiPool.ContainsKey(api.Name))
            {
                ApiPool[api.Name] = api; // Add back to ApiPool
                _log.Information($"[API POOL] Released API '{api.Name}' back to available pool. Now available: {ApiPool.Count}");
                return true;
            }
            else 
                _log.Warning($"[API POOL] API '{api.Name}' could not be released, it is already available.");
            return false;
        }

        // TODO: Don't forget to subscribe this to *all* created HGSs! (and used to display in UI)
        private void HandleHGSError(HedgeGridStrategy strategy, OrderPair pair, OrderResult result)
        {
            _recentErrors.Enqueue($"[{strategy.NameId}] (Pair: {pair.Name} ): Error: {result.Message}");
        }

        // TODO: This also feels incomplete ... shouldn't be at least await strategyToRemove.StopAsync() -ed ?!
        // Should it be consolidated/cancelled all orders/closed all positions also ?!?
        private void HandleStrategyRemovalRequest(HedgeGridStrategy strategyToRemove)
        {
            if (strategyToRemove == null)
                return;
            _log.Information($"[REMOVAL] Request for strategy '{strategyToRemove.NameId}'.");
            if (_strategyPool.TryRemove(strategyToRemove.NameId, out var removedHgs))
            {
                ReleaseApiToPool(removedHgs.ExchangeAPI);
                removedHgs.OnRemovalRequest -= HandleStrategyRemovalRequest;
                if (removedHgs == _currentStrategy)
                {
                    _currentStrategy.OnMarketDataUpdate -= OnMainStrategyFuturesMarketDataUpdate;
                    _currentStrategy = null;
                    UpdateMarketDataSubscription(); 
                }
                (removedHgs as IDisposable)?.Dispose();
                _log.Information($"[REMOVAL] Strategy '{removedHgs.NameId}' removed.");
            }
        }

        // TODO TODO TODO: This doesn't seems quite right, this requires proper discussion!
        public async Task StartAsync()
        {
            if (State != StrategyState.Ready)
            {
                _log.Error($"[START] MainStrategy not Ready. State: {State}");
                return;
            }
            _log.Information("[START] Starting MainStrategy and attempting to start its sub-strategies...");
            
            // Placeholder: Actual starting of reconstructed/blank-slate HGS instances will be driven by InitializeAsync's full logic
            // For now, if any strategies are in _strategyPool and Ready, try to start them.
            // This part will be heavily influenced by the full InitializeAsync implementation.
            bool anyStrategyStarted = false;
            foreach (var kvp in _strategyPool.ToList()) // ToList if collection might change during iteration - No, not suppose to, StartAsync() can be called only when Ready state, which is set only when all the strategies are constructed on InitializeAsync
            {
                if (kvp.Value.State == StrategyState.Ready)
                {
                    await kvp.Value.StartAsync(); 
                    if (kvp.Value.State == StrategyState.Running)
                        anyStrategyStarted = true;
                }
            }

            if (anyStrategyStarted)
            {
                State = StrategyState.Running;
                _log.Information("[START] MainStrategy is now Running with at least one sub-strategy.");
            }
            else
            {
                _log.Warning("[START] MainStrategy StartAsync completed, but no sub-strategies became Running. State remains Ready.");
                // State remains Ready if no sub-strategies started. RunAsync will not block effectively.
            }
        }

        public async Task RunAsync(CancellationToken cancellationToken)
        {
            if (State == StrategyState.Error || State != StrategyState.Ready && State != StrategyState.Running)
            {
                _log.Warning($"RunAsync called but state is {State}");
                return;
            }
            if (State == StrategyState.Running)
            {
                _log.Warning("RunAsync called but already running");
                return;
            }

            // State will be set to Running by StartAsync if successful
            _log.Information("[RUN] MainStrategy RunAsync requested. Will proceed if/when StartAsync sets state to Running.");
            _runCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            try
            {
                // Only wait if running, otherwise StartAsync might not have completed or failed
                if (State == StrategyState.Running)
                    await Task.Delay(Timeout.Infinite, _runCts.Token);
                else
                    _log.Warning("[RUN] MainStrategy not in Running state after StartAsync call. RunAsync will not block.");
            }
            catch (OperationCanceledException) { _log.Information("[RUN] MainStrategy RunAsync cancelled."); }
            catch (Exception ex) { _log.Error(ex, "[RUN] Error in MainStrategy RunAsync."); State = StrategyState.Error; }
            finally
            {
                if (State == StrategyState.Running)
                    State = StrategyState.Stopping;
                _runCts?.Dispose(); _runCts = null;
                _log.Information($"[RUN] MainStrategy RunAsync finished. State: {State}");
            }
        }

        public async Task StopAsync()
        {
            if (State == StrategyState.Stopped || State == StrategyState.Stopping)
            {
                _log.Warning($"MainStrategy already stopping/stopped.");
                return;
            }
            _log.Information("[STOP] MainStrategy stopping all sub-strategies...");
            State = StrategyState.Stopping;
            _runCts?.Cancel(); 

            List<Task> stopTasks = new List<Task>();
            foreach (var strategy in _strategyPool.Values.ToList())
            {
                stopTasks.Add(strategy.StopAsync());
            }
            try
            {
                await Task.WhenAll(stopTasks).WaitAsync(TimeSpan.FromSeconds(15));
            }
            catch (TimeoutException)
            {
                _log.Warning("[STOP] Timeout waiting for sub-strategies to stop.");
            }            
            _log.Information("[STOP] MainStrategy processing finished.");
            State = StrategyState.Stopped;
        }

        // TODO: Regading to this Snapshot, this should be adapted/refactored to show these more 'global' MainStrategy specific informations!
        public StrategyUIData? GetUIDataSnapshot() 
        {
            if (_selectedStrategy == null && _strategyPool.IsEmpty)
            {
                // If no strategies exist at all, reflect this state.
                return new StrategyUIData
                { 
                    StrategyStatus = $"MainStrategy: {this.State} (No active steps or selection)", 
                    APIStatus = ExchangeState.Stopped, // No active API through a strategy
                    Timestamp = DateTime.UtcNow 
                };
            }
            // Primarily report data from the _selectedStrategy (frontier strategy)
            if (_selectedStrategy != null)
            {
                var snap = _selectedStrategy.GetUIDataSnapshot();
                if (snap != null) 
                {
                    snap.StrategyStatus = $"Main(SelHGS:{_selectedStrategy.NameId}): {snap.StrategyStatus}";
                }
                return snap;
            }
            // Fallback if no specific strategy is selected but pool exists (e.g. show aggregate or first)
            var firstStrat = _strategyPool.Values.FirstOrDefault();
            if (firstStrat != null)
            {
                var snap = firstStrat.GetUIDataSnapshot();
                if (snap != null)
                {
                    snap.StrategyStatus = $"Main(FirstHGS:{firstStrat.NameId}): {snap.StrategyStatus}";
                }
                return snap;
            }
            // Should ideally not be reached if the firstIsEmpty check is comprehensive
            return new StrategyUIData { StrategyStatus = $"MainStrategy: {this.State} (Error determining UI data)", Timestamp = DateTime.UtcNow };
        }

        public void SelectSubStrategy(string strategyNameId) 
        {
            if (_strategyPool.TryGetValue(strategyNameId, out var strategyFromPool))
            {
                if (_selectedStrategy != null && _selectedStrategy != strategyFromPool)
                {
                    //_selectedStrategy.OnMarketDataUpdate -= OnMainStrategyFuturesMarketDataUpdate; // no, market data update is from _currentStrategy
                }
                _selectedStrategy = strategyFromPool;
                //_selectedStrategy.OnMarketDataUpdate += OnMainStrategyFuturesMarketDataUpdate; // no, market data update is from _currentStrategy
                _log.Information($"[SELECT] MainStrategy selected '{_selectedStrategy.NameId}' for UI and market data.");
            }
            else { _log.Warning($"[SELECT] Sub-strategy {strategyNameId} not found."); }
        }

        public async Task SetPauseAsync(bool isPaused) 
        {
            _log.Information($"[PAUSE] MainStrategy setting pause to {isPaused} for sub-strategies.");
            foreach (var subStrategy in _strategyPool.Values.ToList())
            {
                await subStrategy.SetPauseAsync(isPaused);
            }
            if (isPaused && State == StrategyState.Running)
                State = StrategyState.Ready; 
            // If unpausing and was Ready, it should go to Running after sub-strategies confirm they are running.
            // This might need more robust state transition logic based on sub-strategy states.
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return; // Check _disposed flag
            if (disposing)
            {
                _log.Information("[DISPOSE] Disposing MainStrategy resources...");
                if (State != StrategyState.Stopped && State != StrategyState.Error)
                {
                    _log.Warning($"[DISPOSE] MainStrategy Dispose called while state is {State}. Attempting to Stop...");
                    StopAsync().Wait(TimeSpan.FromSeconds(5)); // Blocking stop with timeout
                }
                foreach (var strat in _strategyPool.Values.ToList()) { (strat as IDisposable)?.Dispose(); }
                _strategyPool.Clear();
                ApiPool.Clear();
                _runCts?.Cancel(); _runCts?.Dispose();
                _log?.Dispose();
            }
            State = StrategyState.Stopped; // Set to a final, non-Disposed state from StrategyState enum
            _disposed = true; // Set _disposed flag
        }
    }
}
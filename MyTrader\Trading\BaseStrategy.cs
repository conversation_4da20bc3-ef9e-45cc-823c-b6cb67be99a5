using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MyTraderSpace.Logging;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Models;
using Serilog;

namespace MyTraderSpace.Trading.Strategies
{
    /// <summary>
    /// Base class for all trading strategies
    /// </summary>
    public abstract class BaseStrategy : IStrategy
    {
        public string NameId { get; init; }
        public CurrencyPair TradingPair => _exchangeAPI.TradingPair;
        public string Symbol => TradingPair.Symbol;

        public StrategyState State { get; protected set; } = StrategyState.Initializing;

        protected readonly LogManager _baseLog;
        protected readonly BaseExchangeAPI _exchangeAPI;
        protected readonly IMarketDataService _marketDataService;

        public BaseExchangeAPI ExchangeAPI => _exchangeAPI;
        public IMarketDataService MarketDataService => _marketDataService;

        protected BaseStrategy(string nameId, BaseExchangeAPI exchangeAPI, IMarketDataService marketDataService)
        {
            NameId = nameId ?? throw new ArgumentNullException(nameof(nameId));
            _baseLog = new LogManager(GetType().Name); // Use derived type name for logger
            _exchangeAPI = exchangeAPI ?? throw new ArgumentNullException(nameof(exchangeAPI));
            if (_exchangeAPI.State != ExchangeState.Ready)
                throw new InvalidOperationException($"ExchangeAPI is not ready. Current state: {_exchangeAPI.State}");
            _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
            if (!_marketDataService.IsInitialized)
                throw new InvalidOperationException($"MarketDataService is not ready.");
            State = StrategyState.Initializing;
        }

        public abstract Task InitializeAsync();

        public abstract Task StartAsync();

        public abstract Task StopAsync();

        public async Task SetPauseAsync(bool isPaused)
        {
            _baseLog.Information($"[PAUSE] Setting strategy pause state to {isPaused}");
            if (isPaused)
            {
                State = StrategyState.Ready;
            }
            else
            {
                await StartAsync();
            }
        }
    }
} 
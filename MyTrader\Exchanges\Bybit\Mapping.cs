using Bybit.Net.Objects.Models.V5;
using MyTraderSpace.Models;

// Alias Bybit types to avoid confusion
using BybitOrderStatus = Bybit.Net.Enums.OrderStatus;
using BybitOrderSide = Bybit.Net.Enums.OrderSide;
using BybitTimeInForce = Bybit.Net.Enums.TimeInForce;
using BybitCategory = Bybit.Net.Enums.Category;
using BybitNewOrderType = Bybit.Net.Enums.NewOrderType;
using BybitOrderType = Bybit.Net.Enums.OrderType;
using BybitPositionMode = Bybit.Net.Enums.PositionMode;
using BybitPositionStatus = Bybit.Net.Enums.PositionStatus;
using BybitPositionIdx = Bybit.Net.Enums.PositionIdx;
using BybitPositionSide = Bybit.Net.Enums.PositionSide;
using BybitTradeMode = Bybit.Net.Enums.TradeMode;
using BybitAccountType = Bybit.Net.Enums.AccountType;
using BybitMarginMode = Bybit.Net.Enums.MarginMode;
using MyTraderSpace.Utils;

namespace MyTraderSpace.Exchanges.Bybit
{
    public static class MarketDataMapping
    {
        public static SpotMarketData MapToSpotMarket(this BybitSpotTickerUpdate source, decimal bestBid, decimal bestAsk)
        {
            return new SpotMarketData
            {
                Symbol = source.Symbol,
                LastPrice = source.LastPrice,
                HighestBid = bestBid,
                LowestAsk = bestAsk,
                Volume24h = source.Volume24h
            };
        }

        public static FuturesMarketData MapToFuturesMarket(this BybitLinearTickerUpdate source)
        {
            return new FuturesMarketData
            {
                Symbol = source.Symbol,
                LastPrice = source.LastPrice,
                MarkPrice = source.MarkPrice,
                IndexPrice = source.IndexPrice,
                HighestBid = source.BestBidPrice,
                LowestAsk = source.BestAskPrice,
                Volume24h = source.Volume24h,
                OpenInterest = source.OpenInterest,
                FundingRate = source.FundingRate,
                NextFundingTime = source.NextFundingTime
            };
        }

        // Optional: Add methods for other exchange types
        // public static SpotMarketData MapToSpotMarket(this BinanceStreamTicker source) { ... }
        // public static FuturesMarketData MapToFuturesMarket(this BinanceFuturesStreamTicker source) { ... }
    }

    public static class Mapping
    {
        public static BybitCategory MapToBybitCategory(Models.Category category)
        {
            return category switch
            {
                Models.Category.Linear => BybitCategory.Linear,
                Models.Category.Inverse => BybitCategory.Inverse,
                Models.Category.Spot => BybitCategory.Spot,
                Models.Category.Option => BybitCategory.Option,
                _ => throw new ArgumentException($"Unsupported category: {category}")
            };
        }

        public static Models.Category MapFromBybitCategory(BybitCategory category)
        {
            return category switch
            {
                BybitCategory.Linear => Models.Category.Linear,
                BybitCategory.Inverse => Models.Category.Inverse,
                BybitCategory.Spot => Models.Category.Spot,
                BybitCategory.Option => Models.Category.Option,
                _ => Models.Category.Undefined
            };
        }

        public static BybitNewOrderType MapToBybitNewOrderType(Models.OrderType orderType)
        {
            return orderType switch
            {
                Models.OrderType.Market => BybitNewOrderType.Market,
                Models.OrderType.Limit => BybitNewOrderType.Limit,
                _ => BybitNewOrderType.Market
            };
        }

        public static Models.OrderType MapFromBybitNewOrderType(BybitNewOrderType orderType)
        {
            return orderType switch
            {
                BybitNewOrderType.Market => Models.OrderType.Market,
                BybitNewOrderType.Limit => Models.OrderType.Limit,
                _ => Models.OrderType.Market
            };
        }

        public static BybitOrderType MapToBybitOrderType(Models.OrderType orderType)
        {
            return orderType switch
            {
                Models.OrderType.Market => BybitOrderType.Market,
                Models.OrderType.Limit => BybitOrderType.Limit,
                _ => BybitOrderType.Market
            };
        }

        public static Models.OrderType MapFromBybitOrderType(BybitOrderType orderType)
        {
            return orderType switch
            {
                BybitOrderType.Market => Models.OrderType.Market,
                BybitOrderType.Limit => Models.OrderType.Limit,
                _ => Models.OrderType.Market
            };
        }

        public static BybitOrderSide MapToBybitOrderSide(Models.OrderSide side)
        {
            return side switch
            {
                Models.OrderSide.Buy => BybitOrderSide.Buy,
                Models.OrderSide.Sell => BybitOrderSide.Sell,
                _ => BybitOrderSide.Buy
            };
        }

        public static Models.OrderSide MapFromBybitOrderSide(BybitOrderSide side)
        {
            return side switch
            {
                BybitOrderSide.Buy => Models.OrderSide.Buy,
                BybitOrderSide.Sell => Models.OrderSide.Sell,
                _ => Models.OrderSide.Buy
            };
        }

        public static BybitTimeInForce MapToBybitTimeInForce(Models.TimeInForce timeInForce)
        {
            return timeInForce switch
            {
                Models.TimeInForce.GoodTillCancel => BybitTimeInForce.GoodTillCanceled,
                Models.TimeInForce.ImmediateOrCancel => BybitTimeInForce.ImmediateOrCancel,
                Models.TimeInForce.FillOrKill => BybitTimeInForce.FillOrKill,
                Models.TimeInForce.PostOnly => BybitTimeInForce.PostOnly,
                _ => BybitTimeInForce.GoodTillCanceled
            };
        }

        public static Models.TimeInForce MapFromBybitTimeInForce(BybitTimeInForce timeInForce)
        {
            return timeInForce switch
            {
                BybitTimeInForce.GoodTillCanceled => Models.TimeInForce.GoodTillCancel,
                BybitTimeInForce.ImmediateOrCancel => Models.TimeInForce.ImmediateOrCancel,
                BybitTimeInForce.FillOrKill => Models.TimeInForce.FillOrKill,
                BybitTimeInForce.PostOnly => Models.TimeInForce.PostOnly,
                _ => Models.TimeInForce.GoodTillCancel
            };
        }

        /*
        Position-related concepts in Bybit:

        1. PositionMode (Account Setting):
           - MergedSingle: One-way mode (can only hold position in one direction)
           - BothSides: Hedge mode (can hold both long and short positions)

        2. PositionIdx (Position Direction):
           - OneWayMode (0): Position in one-way mode
           - BuyHedgeMode (1): Long position in hedge mode
           - SellHedgeMode (2): Short position in hedge mode

        3. PositionSide (Bybit's enum):
           - Buy: Long position
           - Sell: Short position
           - None: No position

        Our system mapping:

        1. PositionMode:
           - MergedSingle: One-way mode
           - BothSides: Hedge mode

        2. PositionDirection (renamed from PositionSide):
           - OneWay: Position in one-way mode
           - Buy: Long position in hedge mode
           - Sell: Short position in hedge mode
        */

        // Maps Bybit's PositionIdx to our PositionDirection
        public static Models.PositionDirection MapFromBybitPositionIdxToDirection(BybitPositionIdx? idx)
        {
            return idx switch
            {
                BybitPositionIdx.OneWayMode => Models.PositionDirection.OneWay,
                BybitPositionIdx.BuyHedgeMode => Models.PositionDirection.Buy,
                BybitPositionIdx.SellHedgeMode => Models.PositionDirection.Sell,
                _ => Models.PositionDirection.OneWay
            };
        }

        // Maps our PositionDirection to Bybit's PositionIdx
        public static BybitPositionIdx MapToBybitPositionIdx(PositionDirection positionDirection)
        {
            return positionDirection switch
            {
                PositionDirection.OneWay => BybitPositionIdx.OneWayMode,
                PositionDirection.Buy => BybitPositionIdx.BuyHedgeMode,
                PositionDirection.Sell => BybitPositionIdx.SellHedgeMode,
                _ => BybitPositionIdx.OneWayMode
            };
        }

        public static Models.PositionStatus? MapFromBybitPositionStatus(BybitPositionStatus? status)
        {
            return status switch
            {
                BybitPositionStatus.Normal => Models.PositionStatus.Normal,
                BybitPositionStatus.Liqidation => Models.PositionStatus.Liqidation,
                BybitPositionStatus.AutoDeleverage => Models.PositionStatus.AutoDeleverage,
                _ => Models.PositionStatus.Normal
            };
        }

        public static Models.OrderStatus MapFromBybitOrderStatus(BybitOrderStatus status)
        {
            return status switch
            {
                BybitOrderStatus.Created => Models.OrderStatus.Created,
                BybitOrderStatus.New => Models.OrderStatus.New,
                BybitOrderStatus.Rejected => Models.OrderStatus.Rejected,
                BybitOrderStatus.PartiallyFilled => Models.OrderStatus.PartiallyFilled,
                BybitOrderStatus.Filled => Models.OrderStatus.Filled,
                BybitOrderStatus.Cancelled => Models.OrderStatus.Cancelled,
                BybitOrderStatus.Untriggered => Models.OrderStatus.Untriggered,
                _ => Models.OrderStatus.Untriggered
            };
        }

        public static Models.TradeMode MapFromBybitTradeMode(BybitTradeMode tradeMode)
        {
            return tradeMode switch
            {
                BybitTradeMode.Isolated => Models.TradeMode.Isolated,
                BybitTradeMode.CrossMargin => Models.TradeMode.CrossMargin,
                _ => Models.TradeMode.CrossMargin
            };
        }

        public static BybitTradeMode MapToBybitTradeMode(Models.TradeMode tradeMode)
        {
            return tradeMode switch
            {
                Models.TradeMode.Isolated => BybitTradeMode.Isolated,
                Models.TradeMode.CrossMargin => BybitTradeMode.CrossMargin,
                _ => BybitTradeMode.CrossMargin
            };
        }

        public static BybitMarginMode MapToBybitMarginMode(Models.MarginMode marginMode)
        {
            return marginMode switch
            {
                Models.MarginMode.RegularMargin => BybitMarginMode.RegularMargin,
                Models.MarginMode.PortfolioMargin => BybitMarginMode.PortfolioMargin,
                Models.MarginMode.IsolatedMargin => BybitMarginMode.IsolatedMargin,
                _ => BybitMarginMode.RegularMargin
            };
        }

        public static BybitPositionMode MapToBybitPositionMode(Models.PositionMode positionMode)
        {
            return positionMode switch
            {
                Models.PositionMode.MergedSingle => BybitPositionMode.MergedSingle,
                Models.PositionMode.BothSides => BybitPositionMode.BothSides,
                _ => BybitPositionMode.BothSides
            };
        }

        public static Models.PositionMode MapFromBybitPositionMode(BybitPositionMode mode)
        {
            return mode switch
            {
                BybitPositionMode.MergedSingle => Models.PositionMode.MergedSingle,
                BybitPositionMode.BothSides => Models.PositionMode.BothSides,
                _ => Models.PositionMode.MergedSingle
            };
        }

        // Maps Bybit's PositionSide to our PositionSide
        public static Models.PositionSide? MapFromBybitPositionSide(BybitPositionSide? side)
        {
            return side switch
            {
                BybitPositionSide.Buy => Models.PositionSide.Buy,
                BybitPositionSide.Sell => Models.PositionSide.Sell,
                BybitPositionSide.None => Models.PositionSide.None,
                _ => null
            };
        }

        // Maps our PositionSide to Bybit's PositionSide
        public static BybitPositionSide MapToBybitPositionSide(Models.PositionSide side)
        {
            return side switch
            {
                Models.PositionSide.Buy => BybitPositionSide.Buy,
                Models.PositionSide.Sell => BybitPositionSide.Sell,
                Models.PositionSide.None => BybitPositionSide.None,
                _ => BybitPositionSide.None
            };
        }
    }

    public static class UpdateMapping
    {
        public static PositionModelUpdate MapPosition(BybitPositionUpdate source)
        {
            return new PositionModelUpdate
            {
                // TODO: verify all properties are mapped
                Category = Mapping.MapFromBybitCategory(source.Category),
                Direction = Mapping.MapFromBybitPositionIdxToDirection(source.PositionIdx),
                Side = Mapping.MapFromBybitPositionSide(source.Side),
                Symbol = source.Symbol,
                Quantity = source.Quantity,
                AveragePrice = source.AveragePrice,
                PositionValue = source.PositionValue,
                TradeMode = Mapping.MapFromBybitTradeMode(source.TradeMode),
                PositionStatus = Mapping.MapFromBybitPositionStatus(source.PositionStatus),
                Leverage = source.Leverage,
                MarkPrice = source.MarkPrice,
                LiquidationPrice = source.LiquidationPrice,
                BustPrice = source.BustPrice,
                InitialMargin = source.InitialMargin,
                MaintenanceMargin = source.MaintenanceMargin,
                TakeProfit = source.TakeProfit,
                StopLoss = source.StopLoss,
                UnrealizedPnl = source.UnrealizedPnl,
                RealizedPnl = source.RealizedPnl,
                CurrentRealizedPnl = source.CurrentRealizedPnl,
                CreateTime = source.CreateTime,
                UpdateTime = source.UpdateTime,
                AutoAddMargin = source.AutoAddMargin,
                PositionBalance = source.PositionBalance
            };
        }

        public static OrderModel MapOrder(BybitOrder source)
        {
            return new OrderModel
            {
                // TODO: verify all properties are mapped
                OrderId = source.OrderId,
                ClientOrderId = source.ClientOrderId,
                Symbol = source.Symbol,
                Side = Mapping.MapFromBybitOrderSide(source.Side),
                OrderType = Mapping.MapFromBybitOrderType(source.OrderType),
                Price = source.Price ?? 0,
                Quantity = source.Quantity,
                AveragePrice = source.AveragePrice,
                QuantityFilled = source.QuantityFilled ?? 0,
                QuantityRemaining = source.QuantityRemaining,
                Status = Mapping.MapFromBybitOrderStatus(source.Status),
                TimeInForce = Mapping.MapFromBybitTimeInForce(source.TimeInForce),
                IsLeverage = source.IsLeverage,
                TakeProfit = source.TakeProfit,
                StopLoss = source.StopLoss,
                TakeProfitLimitPrice = source.TakeProfitLimitPrice,
                StopLossLimitPrice = source.StopLossLimitPrice,
                TriggerPrice = source.TriggerPrice,
                RejectReason = source.RejectReason,
                CreateTime = source.CreateTime,
                UpdateTime = source.UpdateTime
            };
        }

        public static WalletUpdate MapWalletUpdate(BybitBalance source)
        {
            return new WalletUpdate
            {
                AccountType = MapFromBybitAccountType(source.AccountType),
                AccountInitialMarginRate = source.AccountInitialMarginRate,
                AccountMaintenanceMarginRate = source.AccountMaintenanceMarginRate,
                TotalEquity = source.TotalEquity,
                TotalWalletBalance = source.TotalWalletBalance,
                TotalMarginBalance = source.TotalMarginBalance,
                TotalAvailableBalance = source.TotalAvailableBalance,
                TotalPerpUnrealizedPnl = source.TotalPerpUnrealizedPnl,
                TotalInitialMargin = source.TotalInitialMargin,
                TotalMaintenanceMargin = source.TotalMaintenanceMargin,
                Assets = MapAssetBalances(source.Assets)
            };
        }

        // Made public to be accessible from BybitExchangeAPI
        public static AccountType MapFromBybitAccountType(BybitAccountType bybitAccountType)
        {
            return bybitAccountType switch
            {
                BybitAccountType.Unified => AccountType.Unified,
                BybitAccountType.Fund => AccountType.Funding,
                BybitAccountType.Spot => AccountType.Spot,
                BybitAccountType.Contract => AccountType.Futures, // TODO: Check if this is the best match, or BybitAccountType.Option ?!
                //BybitAccountType. => AccountType.Margin,        // no corresponding
                BybitAccountType.Option => AccountType.Options,
                _ => AccountType.Unified
            };
        }

        private static IEnumerable<AssetBalance> MapAssetBalances(IEnumerable<BybitAssetBalance>? assets)
        {
            if (assets == null)
                return Array.Empty<AssetBalance>();

            return assets.Select(asset => new AssetBalance
            {
                Asset = asset.Asset,
                Total = asset.WalletBalance ?? 0,
                Available = asset.Free ?? 0,
                InOrder = asset.Locked ?? 0
            });
        }

        public static OrderModelUpdate MapOrderUpdate(BybitOrderUpdate source)
        {
            // Handle empty or null FeeAsset
            string feeAsset = string.IsNullOrEmpty(source.FeeAsset) ? "USDT" : source.FeeAsset;
            
            // TODO: Implement a more elegant solution for fee currency mapping
            // Current implementation makes a best effort to map fees correctly:
            // 1. For futures (Linear), fees are in the settlement currency (typically USDT)
            // 2. For spot, fees can be in either base or quote currency depending on the trade
            // A better solution would use market metadata to determine base/quote currencies
            // and properly map fees based on the trading pair and operation type
            
            Fee executedFee;
            
            if (source.Category == BybitCategory.Linear)
            {
                // For futures, fee is in settlement currency (USDT)
                executedFee = new Fee(
                    new CurrencyAmount(CoinType.BTC, 0), // Base currency amount is 0
                    new CurrencyAmount(Helpers.ParseCoin(feeAsset), source.ExecutedFee ?? 0) // Fee in quote currency
                );
            }
            else if (source.Category == BybitCategory.Spot)
            {
                // For spot trading, we need to determine if the fee is in base or quote currency
                // This is based on the FeeAsset provided by Bybit
                
                // Extract base and quote from symbol (e.g., BTCUSDT -> BTC and USDT)
                string baseAsset = string.Empty;
                string quoteAsset = string.Empty;
                
                if (!string.IsNullOrEmpty(source.Symbol))
                {
                    if (source.Symbol.EndsWith("USDT"))
                    {
                        baseAsset = source.Symbol.Substring(0, source.Symbol.Length - 4);
                        quoteAsset = "USDT";
                    }
                    else if (source.Symbol.EndsWith("USDC"))
                    {
                        baseAsset = source.Symbol.Substring(0, source.Symbol.Length - 4);
                        quoteAsset = "USDC";
                    }
                    else if (source.Symbol.EndsWith("USD"))
                    {
                        baseAsset = source.Symbol.Substring(0, source.Symbol.Length - 3);
                        quoteAsset = "USD";
                    }
                    else if (source.Symbol.Length > 3)
                    {
                        // Fallback: assume last 3-4 characters are quote currency
                        baseAsset = source.Symbol.Substring(0, source.Symbol.Length - 3);
                        quoteAsset = source.Symbol.Substring(source.Symbol.Length - 3);
                    }
                }
                
                // If fee asset matches base asset, fee is in base currency
                if (string.Equals(feeAsset, baseAsset, StringComparison.OrdinalIgnoreCase))
                {
                    executedFee = new Fee(
                        new CurrencyAmount(Helpers.ParseCoin(feeAsset), source.ExecutedFee ?? 0), // Fee in base currency
                        new CurrencyAmount(Helpers.ParseCoin(quoteAsset), 0) // Quote currency amount is 0
                    );
                }
                // Otherwise, assume fee is in quote currency
                else
                {
                    executedFee = new Fee(
                        new CurrencyAmount(Helpers.ParseCoin(baseAsset), 0), // Base currency amount is 0
                        new CurrencyAmount(Helpers.ParseCoin(feeAsset), source.ExecutedFee ?? 0) // Fee in quote currency
                    );
                }
            }
            else
            {
                // For other categories, default to fee in quote currency
                executedFee = new Fee(
                    new CurrencyAmount(CoinType.BTC, 0), // Base currency amount is 0
                    new CurrencyAmount(Helpers.ParseCoin(feeAsset), source.ExecutedFee ?? 0) // Fee in specified asset
                );
            }
            
            return new OrderModelUpdate
            {
                // TODO: verify all properties are mapped
                OrderId = source.OrderId,
                ClientOrderId = source.ClientOrderId,
                Symbol = source.Symbol,
                Category = Mapping.MapFromBybitCategory(source.Category),
                Price = source.Price,
                Quantity = source.Quantity,
                Side = Mapping.MapFromBybitOrderSide(source.Side),
                IsLeverage = source.IsLeverage,
                Status = Mapping.MapFromBybitOrderStatus(source.Status),
                RejectReason = source.RejectReason,
                AveragePrice = source.AveragePrice,
                QuantityRemaining = source.QuantityRemaining,
                ValueRemaining = source.ValueRemaining,
                QuantityFilled = source.QuantityFilled,
                ValueFilled = source.ValueFilled,
                ExecutedFee = executedFee,
                TimeInForce = Mapping.MapFromBybitTimeInForce(source.TimeInForce),
                OrderType = Mapping.MapFromBybitOrderType(source.OrderType),
                TriggerPrice = source.TriggerPrice,
                TakeProfit = source.TakeProfit,
                StopLoss = source.StopLoss,
                UpdateTime = source.UpdateTime,
                TakeProfitLimitPrice = source.TakeProfitLimitPrice,
                StopLossLimitPrice = source.StopLossLimitPrice,
                ClosedPnl = source.ClosedPnl,
                CreateTime = source.CreateTime
            };
        }

        public static PositionModel MapPositionFromBybitPosition(BybitPosition position)
        {
            // Infer PositionMode from PositionIdx
            var positionMode = position.PositionIdx == BybitPositionIdx.OneWayMode 
                ? Models.PositionMode.MergedSingle 
                : Models.PositionMode.BothSides;

            return new PositionModel
            {
                // TODO: verify all properties are mapped
                Category = Mapping.MapFromBybitCategory(position.Category),
                Symbol = position.Symbol,
                Direction = Mapping.MapFromBybitPositionIdxToDirection(position.PositionIdx),
                Side = Mapping.MapFromBybitPositionSide(position.Side),
                Quantity = position.Quantity,
                AveragePrice = position.AveragePrice,
                PositionValue = position.PositionValue,
                TradeMode = Mapping.MapFromBybitTradeMode(position.TradeMode),
                PositionStatus = Mapping.MapFromBybitPositionStatus(position.PositionStatus),
                Leverage = position.Leverage,
                MarkPrice = position.MarkPrice,
                LiquidationPrice = position.LiquidationPrice,
                BustPrice = position.BustPrice,
                InitialMargin = position.InitialMargin,
                MaintenanceMargin = position.MaintenanceMargin,
                TakeProfit = position.TakeProfit,
                StopLoss = position.StopLoss,
                UnrealizedPnl = position.UnrealizedPnl,
                RealizedPnl = position.RealizedPnl,
                CurrentRealizedPnl = position.CurrentRealizedPnl,
                CreateTime = position.CreateTime,
                UpdateTime = position.UpdateTime,
                AutoAddMargin = position.AutoAddMargin,
                PositionBalance = position.PositionBalance,
                PositionMode = positionMode // Set the inferred position mode
            };
        }
    }
} 
﻿Here is what I thought as the architecture of the trading system:

I would like to have a system that is as flexible to various trading strategies as possible, so the followings what I enumerate
in first round of implementation probably should be interfaces and/or abstract classes.

 * ExchangeTrader would be able to run an implementation of IStrategy or BaseStrategy (abstract class) - from now on let's call this just 'Strategy'
 * First of all a Strategy would have injected an IMarketDataService for having/being able to have it's own ticker data handling/processing!
 * In Strategy SpotPool, and FuturesPool is also 'injected'
 * Strategy, based on the 'availability' of SpotPool or FuturesPool elements is able to create a new entity (interface of abstract class, probably the latter)
   this entity could be called TradePoint ...
 * This TradePoint would have injected also a IMarketDataService, and it will have it's own ticker data handling/processing!
 * This TradePoint would have a setting defining in what @Types.cs->TradeMode and PositionMode(MergedSingle or BothSides) it is able to do it's own 'local' trade (since we have ticker data provided and handled as mentioned)
 * This TradePoint would 'expose' important state parameters, like balances, PnL's, liquidation prices, accumulated fees, etc! (exposes means they are queryable with a function, or in any 'proper way') for the reason that 'main' Strategy is able to monitor these values and act accordingly
 * Strategy based on the trading conditions 'requests' the creation of a new TradePoint from the pool, and when this new tradePoint
   was created, the BaseExchanegAPI element from the pool is removed. Also when certain trade conditions are met, the TradePoint can be 'destroyed' in this way 'releasing' back its BaseExchangeAPI instance to the pool.
   This can be done by requested by Strategy, or even a TradePoint can have a 'self-destroy' request, (based on its internal conditions)
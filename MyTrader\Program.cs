﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Threading;
using Microsoft.Extensions.DependencyInjection;
using MyTraderSpace.Logging;
using MyTraderSpace.Config;
using MyTraderSpace.Models;
using MyTraderSpace.Exchanges;
using MyTraderSpace.UI;

namespace MyTraderSpace
{
    public class Program
    {
        private static TextWriter _originalConsoleOut;
        private static TerminalConsoleInterface _ui;
        private static ExchangeTrader _trader;
        private static CancellationTokenSource _cts;
        private static volatile bool _shutdownSignaled = false;
        private static volatile bool _shutdownSequenceRunning = false;

        private static AppConfig _config;
        private static CurrencyPair _tradingPair;
        private static string CONFIG_PATH = "config.json";
        private static string STRATEGIES_CONFIG_PATH = "strategies.json";

        private static SimulationMode _uiSimMode = SimulationMode.Normal;
        private static Task _traderTask = Task.CompletedTask;
        private static Task _uiTask = Task.CompletedTask;

        private static LogManager Log;

        private enum ShutdownReason { CtrlC, UIShutdownRequest, UIFailure, TraderCompleted, UICompleted, UpdateLoopFailure, ExitCommand, StartupError, Cleanup }

        private static ConfigurationLoader _configLoader;

        public static async Task Main(string[] args)
        {
            _originalConsoleOut = Console.Out;
            Console.WriteLine("--- MyTrader Application Starting ---");

            try
            {
                if (args.Contains("--create-keys"))
                {
                    CreateApiKeys();
                    return;
                }

                _cts = new CancellationTokenSource();
                Console.CancelKeyPress += OnCancelKeyPress;

                _configLoader = new ConfigurationLoader(CONFIG_PATH, STRATEGIES_CONFIG_PATH);
                await _configLoader.LoadConfigAsync();
                _config = _configLoader.GetAppConfig();

                // Configure the main logger instance based on loaded config
                SetupInitialLogging(); // This will log its own success/failure

                // Process command line arguments to get mode and potential data directory override
                string mode = ProcessCommandLineArguments(args, out var dataDirectory);
                
                // Override config with command-line data directory if provided for Simulation mode
                if (!string.IsNullOrEmpty(dataDirectory) && mode.Equals("Simulation", StringComparison.OrdinalIgnoreCase))
                {
                    if (_config.Modes.TryGetValue("Simulation", out var simModeConfig) && simModeConfig.MarketData != null)
                    {
                        Log?.Information($"Overriding Simulation MarketData DefaultDataDirectory from config ('{simModeConfig.MarketData.DefaultDataDirectory}') with command-line argument: '{dataDirectory}'");
                        simModeConfig.MarketData.DefaultDataDirectory = dataDirectory;
                    }
                    else
                    {
                        Log?.Warning($"Command-line argument --sim {dataDirectory} provided, but Simulation mode or its MarketData config is missing. Override failed.");
                    }
                }
                // Note: _dataDirectory variable itself is no longer needed beyond this point

                Log!.Information($"Starting MyTrader in {mode} mode...");
                
                _tradingPair = _config.TradingPairParsed; // Use the parsed value
                Log.Information($"Trading pair: {_tradingPair.Symbol}");

                // Check if the selected mode exists in the configuration
                if (!_config.Modes.TryGetValue(mode, out var modeConfig))
                {
                    Log.Error($"Mode '{mode}' not found in configuration. Please check config.json.");
                    await WaitForEnterBeforeExitAsync("Configuration error. Press Enter to exit...", _originalConsoleOut);
                    return;
                }

                await InitializeAllAsync(mode);

                Log.Information("[Main] Entering Running Phase...");

                // Start the trader task (UI task is started by _ui.StartUIThread() in Init)
                _traderTask = _trader.RunAsync();

                Log.Information("[Main] Waiting for UI or Trader task completion...");
                // Wait for EITHER the UI task OR the Trader task to complete
                Task completedPrimaryTask = await Task.WhenAny(_uiTask, _traderTask);

                HandlePrimaryTaskCompletion(completedPrimaryTask);

                Log.Information("[Main] Primary task completed. Awaiting shutdown in finally block...");

            }
            catch (OperationCanceledException) when (_cts != null && _cts.IsCancellationRequested)
            {
                // Logger might not be initialized if cancellation happened early
                (Log ?? new LogManager("Program")).Warning("[Main] Operation canceled during execution.");
                if (Console.Out != _originalConsoleOut)
                    Console.SetOut(_originalConsoleOut);
                SignalShutdown(ShutdownReason.Cleanup);
            }
            catch (Exception ex)
            {
                 // Logger might not be initialized if exception happened early
                (Log ?? new LogManager("Program")).Error(ex, "[Main] CRITICAL STARTUP/RUNTIME ERROR");
                if (Console.Out != _originalConsoleOut)
                    Console.SetOut(_originalConsoleOut);
                SignalShutdown(ShutdownReason.StartupError);
                await WaitForEnterBeforeExitAsync("Critical error during execution. Press Enter to exit.", _originalConsoleOut);
            }
            finally
            {
                Log?.Information("--- Entering Main Finally Block ---");
                await PerformShutdownSequenceAsync();
                Log?.Information("--- Application Finished ---");
                await WaitForEnterBeforeExitAsync("Execution finished. Press Enter to close window.", _originalConsoleOut, 5);
            }
        }

        private static async Task InitializeAllAsync(string modeName)
        {
            Log.Information("[Init] Starting initialization sequence...");
            IUIDataProvider? dataProvider = null; // Variable to hold the data provider

            try
            {
                // 1. Create and Initialize Trader (which creates MainStrategy)
                Log.Information("[Init] Initializing Trader...");
                _trader = new ExchangeTrader(_configLoader, modeName);
                await _trader.InitializeAsync();
                dataProvider = _trader.DataProvider; // Get the provider from the Trader
                Log.Information("[Init] Trader Initialized Successfully.");

                // 2. Create and Initialize UI, passing the data provider
                if (dataProvider == null)
                {
                    throw new InvalidOperationException("Failed to get IUIDataProvider from initialized trader.");
                }
                Log.Information("[Init] Initializing UI...");
                _ui = new TerminalConsoleInterface(_cts.Token, _originalConsoleOut, dataProvider, _uiSimMode);
                _ui.ShutdownRequested += OnShutdownRequested;
                _ui.UIFailed += OnUIFailed;
                _ui.CommandEntered += OnCommandEntered;
                await _ui.InitializeAsync();
                _ui.StartUIThread();
                await _ui.WaitForConsoleReadyAsync();
                Console.SetOut(_ui.Writer);
                Log.Information("--- UI Initialized Successfully, Console Output Redirected to UI ---");
                _uiTask = _ui.UITask;

            }
            catch (Exception ex)
            {
                Log.Error(ex, "[Init] CRITICAL INIT ERROR during component initialization.");

                // --- Refined Cleanup for Initialization Failure ---
                if (Console.Out != _originalConsoleOut)
                {
                    Console.SetOut(_originalConsoleOut); // Restore console immediately if UI failed after redirect
                    Log.Information("[Init Error Cleanup] Console output restored.");
                }

                // Attempt cleanup of partially initialized components
                if (_ui != null)
                {
                    Log.Information("[Init Error Cleanup] Detaching UI event handlers...");
                    // Detach handlers to prevent calls on potentially disposed objects later
                    _ui.ShutdownRequested -= OnShutdownRequested;
                    _ui.UIFailed -= OnUIFailed;
                    _ui.CommandEntered -= OnCommandEntered;
                    try
                    {
                        Log.Information("[Init Error Cleanup] Disposing UI...");
                        // Request stop quickly, then dispose
                        _ui.RequestStop();
                        await _ui.ShutdownAsync().WaitAsync(TimeSpan.FromSeconds(1)); // Very short wait
                        _ui.Dispose();
                    }
                    catch (Exception uiEx)
                    {
                        Log.Error(uiEx, "[Init Error Cleanup] Error stopping/disposing UI.");
                    }
                    _ui = null;
                }
                if (_trader != null)
                {
                    // Trader initialization happens first. If UI fails, Trader might exist.
                    // Trader should ideally clean up its own subscriptions in StopAsync/Dispose.
                    try
                    {
                        Log.Information("[Init Error Cleanup] Stopping and Disposing Trader...");
                        // Try a quick stop/dispose; main shutdown handles more robustly if this fails.
                        _trader.RequestStop();
                        await _trader.StopAsync().WaitAsync(TimeSpan.FromSeconds(2)); // Short timeout
                        (_trader as IDisposable)?.Dispose();
                    }
                    catch (Exception traderEx)
                    {
                        Log.Error(traderEx, "[Init Error Cleanup] Error stopping/disposing Trader.");
                    }
                    _trader = null; // Nullify even if dispose failed
                }
                // --- End Refined Cleanup ---

                SignalShutdown(ShutdownReason.StartupError); // Signal shutdown *after* attempting local cleanup
                throw; // Re-throw the original exception to be caught by Main's handler
            }

            Log.Information("[Init] All components initialized successfully.");
        }

        private static void HandlePrimaryTaskCompletion(Task completedTask)
        {
            if (completedTask == _uiTask && Console.Out != _originalConsoleOut)
            {
                Console.SetOut(_originalConsoleOut);
                Log.Information("[Main] Console output restored due to UI task completion.");
            }

            if (completedTask == _uiTask)
            {
                Log.Information($"[Main] UI Task completed. Status: {completedTask.Status}");
                if (completedTask.IsFaulted)
                    Log.Error(completedTask.Exception?.Flatten(), "[Main] UI Task failed.");
                SignalShutdown(ShutdownReason.UICompleted);
            }
            else // Trader task completed
            {
                Log.Information($"[Main] Trader Task completed. Status: {completedTask.Status}");
                if (completedTask.IsFaulted)
                    Log.Error(completedTask.Exception?.Flatten(), "[Main] Trader Task failed.");
                SignalShutdown(ShutdownReason.TraderCompleted);
            }
        }

        private static async Task PerformShutdownSequenceAsync()
        {
            if (_shutdownSequenceRunning)
            {
                Log?.Debug("[Shutdown] Sequence already in progress. Skipping.");
                return;
            }
            _shutdownSequenceRunning = true;

            if (Console.Out != _originalConsoleOut)
            {
                Console.SetOut(_originalConsoleOut);
                Log?.Information("[Shutdown] Original console restored at sequence start.");
            }
            else
            {
                Log?.Debug("[Shutdown] Console was already original at sequence start.");
            }

            Log?.Information("[Shutdown] Performing shutdown sequence...");

            Log?.Information("[Shutdown] Ensuring stop signals sent...");
            SignalShutdown(ShutdownReason.Cleanup); // Ensure cancellation is signaled

            Log?.Information("[Shutdown] Waiting for Trader RunAsync Task...");
            try
            {
                if (_traderTask != null && !_traderTask.IsCompleted)
                    await _traderTask.WaitAsync(TimeSpan.FromSeconds(5));
                Log?.Information("[Shutdown] Trader RunAsync Task finished or timed out.");
            }
            catch (TimeoutException)
            {
                Log?.Warning("[Shutdown] Timeout waiting for Trader RunAsync Task (5s).");
            }
            catch (Exception ex)
            {
                Log?.Error(ex, "[Shutdown] Error waiting for Trader Task.");
            }

            Log?.Information("[Shutdown] Shutting down Trader components (will dispose MarketDataService)...");
            try
            {
                if (_trader != null)
                {
                    await _trader.StopAsync().WaitAsync(TimeSpan.FromSeconds(10));
                    Log?.Information("[Shutdown] Trader StopAsync complete or timed out.");
                    (_trader as IDisposable)?.Dispose(); // Trader Dispose handles service dispose
                    Log?.Information("[Shutdown] Trader disposed.");
                }
                else
                {
                    Log?.Information("[Shutdown] Trader was already null. Skipping shutdown/dispose.");
                }
            }
            catch (TimeoutException)
            {
                Log?.Warning("[Shutdown] Timeout during Trader StopAsync (10s).");
            }
            catch (Exception ex)
            { 
                Log?.Error(ex, "[Shutdown] Error during Trader StopAsync/Dispose.");
            }
            _trader = null;

            Log?.Information("[Shutdown] Shutting down UI components...");
            try
            {
                if (_ui != null)
                {
                    await _ui.ShutdownAsync().WaitAsync(TimeSpan.FromSeconds(5));
                    Log?.Information("[Shutdown] UI ShutdownAsync complete or timed out.");
                    _ui.Dispose();
                    Log?.Information("[Shutdown] UI disposed.");
                }
                else
                {
                    Log?.Information("[Shutdown] UI was already null. Skipping shutdown/dispose.");
                }
            }
            catch (TimeoutException)
            {
                Log?.Warning("[Shutdown] Timeout during UI ShutdownAsync (5s).");
            }
            catch (Exception ex)
            {
                Log?.Error(ex, "[Shutdown] Error during UI ShutdownAsync/Dispose.");
            }
            _ui = null;

            Log?.Information("[Shutdown] Disposing CancellationTokenSource...");
            try
            {
                _cts?.Dispose();
            }
            catch (Exception ex)
            {
                Log?.Error(ex, "[Shutdown] Error disposing CTS.");
            }
            _cts = null;

            Log?.Information("[Shutdown] Shutdown sequence finished.");
            _shutdownSequenceRunning = false;
        }

        private static void OnCancelKeyPress(object sender, ConsoleCancelEventArgs e)
        {
            Log?.Information("[Handler] Ctrl+C detected!");
            e.Cancel = true; 

            bool uiLikelyActive = _ui != null && _uiTask != null && !_uiTask.IsCompleted;

            if (!uiLikelyActive)
            {
                Log?.Information("[Handler] Ctrl+C: UI not fully active. Signaling shutdown directly.");
                SignalShutdown(ShutdownReason.CtrlC);
            } else {
                Log?.Information("[Handler] Ctrl+C: UI appears active. Assuming UI handles Ctrl+C internally (mapping to ShutdownRequested).");
                // Don't signal shutdown here; let the UI handle it via ShutdownRequested event
            }
        }

        private static void OnShutdownRequested(object sender, EventArgs e)
        {
            Log?.Information("[Handler] UI Shutdown Requested (e.g., Ctrl+Q). Signaling shutdown.");
            SignalShutdown(ShutdownReason.UIShutdownRequest);
        }

        private static void OnUIFailed(object sender, Exception ex)
        {
            Log?.Error(ex, "[Handler] CRITICAL UI FAILURE");
            if (Console.Out != _originalConsoleOut)
            {
                Console.SetOut(_originalConsoleOut);
                Log?.Information("[Handler] Console restored due to UI failure.");
            }
            SignalShutdown(ShutdownReason.UIFailure);
        }

        private static void OnCommandEntered(object sender, string cmd)
        {
            if (_shutdownSignaled || _shutdownSequenceRunning)
            {
                Log?.Debug($"[Handler] Command '{cmd}' ignored, shutdown in progress.");
                return;
            }

            Log?.Information($"[Handler] Command received: {cmd}");

            if (cmd.Trim().Equals("exit", StringComparison.OrdinalIgnoreCase) || cmd.Trim().Equals("quit", StringComparison.OrdinalIgnoreCase))
            {
                Log?.Information("[Handler] 'exit'/'quit' command received, signaling shutdown.");
                SignalShutdown(ShutdownReason.ExitCommand);
            }
            else
            {
                HandleRuntimeCommand(cmd);
            }
        }

        private static void SignalShutdown(ShutdownReason reason)
        {
            if (_shutdownSignaled && reason != ShutdownReason.Cleanup) // Allow cleanup signal multiple times
            {
                Log?.Debug($"[SignalShutdown] Ignored ({reason}): Shutdown already signaled.");
                return;
            }

            Log?.Information($"[SignalShutdown] Shutdown signaled due to: {reason}. Sending stop requests...");

            _shutdownSignaled = true; 

            try { _cts?.Cancel(); } catch (ObjectDisposedException) { /* Ignore */ }
            try { _ui?.RequestStop(); } catch { /* Ignore */ }
            try { _trader?.RequestStop(); } catch { /* Ignore */ }
        }

        // Instantiates the main LogManager instance. Configuration needs specific LogManager API.
        private static void SetupInitialLogging()
        {
            LogLevel minLevel = LogLevel.Information; // Default
            bool consoleEnabled = true;             // Default
            bool fileEnabled = true;                // Default
            string? filePathToPass = null;            // Default to null to trigger automatic path
            Serilog.RollingInterval interval = Serilog.RollingInterval.Day; // Default
            
            if (_config?.Logging != null)
            {
                // Attempt to parse settings from config
                if (!Enum.TryParse<LogLevel>(_config.Logging.MinimumLevel, true, out minLevel))
                {
                    Console.WriteLine($"[Warning] Invalid MinimumLevel '{_config.Logging.MinimumLevel}' in logging config. Using default: {minLevel}.");
                }
                if (!Enum.TryParse<Serilog.RollingInterval>(_config.Logging.RollingInterval, true, out interval))
                {
                    Console.WriteLine($"[Warning] Invalid RollingInterval '{_config.Logging.RollingInterval}' in logging config. Using default: {interval}.");
                }
                consoleEnabled = _config.Logging.ConsoleEnabled;
                fileEnabled = _config.Logging.FileEnabled;
                
                // Use explicit path only if file logging is enabled and path is provided
                if (fileEnabled && !string.IsNullOrWhiteSpace(_config.Logging.FilePath))
                {
                    filePathToPass = _config.Logging.FilePath; 
                }
            }
            else
            {
                Console.WriteLine("[Warning] Logging configuration missing. Using default LogManager settings.");
                // Defaults set above will be used, including filePathToPass = null
            }

            try
            {
                // Instantiate LogManager with determined settings
                Log = new LogManager(
                    "Program", 
                    minLevel, 
                    consoleEnabled, 
                    fileEnabled, 
                    filePathToPass, // Pass null or the explicit path
                    interval
                );
            }
            catch (Exception ex)
            {
                // If LogManager constructor fails critically, fallback to absolute defaults
                Console.WriteLine($"[Critical Error] Failed to instantiate LogManager with parsed/default config: {ex.Message}. Falling back to absolute defaults.");
                Log = new LogManager("Program"); // Use constructor defaults (which now include filePath=null)
            }
        }

        private static string ProcessCommandLineArguments(string[] args, out string dataDirectory)
        {
            dataDirectory = null;
            string mode = _config.DefaultMode;

            for (int i = 0; i < args.Length; i++)
            {
                string arg = args[i].ToLower();
                switch (arg)
                {
                    case "--demo": mode = "Demo"; break;
                    case "--live": mode = "Live"; break;
                    case "--sim":
                        mode = "Simulation";
                        if (i + 1 < args.Length && !args[i + 1].StartsWith("--"))
                        {
                            dataDirectory = args[i + 1]; i++;
                        }
                        break;
                }
            }
            // Log selection after logger is potentially configured
            // Log?.Information($"Selected Mode: {mode}"); 
            // if (dataDirectory != null)
            //     Log?.Information($"Data Directory override: {dataDirectory}");
            return mode;
        }

        private static void HandleRuntimeCommand(string command)
        {
            if (_shutdownSignaled || _shutdownSequenceRunning)
            {
                Log?.Debug($"[HandleRuntimeCommand] Shutdown signaled. Command '{command}' ignored.");
                return;
            }

            if (_trader == null)
            {
                Log?.Warning("[HandleRuntimeCommand] Cannot process command: Trader not available.");
                return;
            }
            
            var api = _trader?.GetSelectedApi(); // Use already selected API

            switch (command.ToLower().Trim())
            {
                case "status":
                    Log?.Information($"[Command] Trader Status: {_trader?.TraderState}");
                    break;
                case "balance":
                    if (api != null)
                    {
                        Task.Run(async () => { 
                            try
                            {
                                var balance = await api.GetWalletBalancesAsync();
                                Log?.Information($"[Command] Balance ({api.Name ?? api.GetType().Name}): {balance.TotalEquity:F2}");
                            }
                            catch (Exception ex)
                            {
                                Log?.Error(ex, "[Command] Error getting balance.");
                            }
                        });
                    } else { Log?.Warning("[Command] Cannot get balance: No API selected."); }
                    break;
                case "help":
                    Log?.Information("[Command] Available commands: status, balance, help, exit, quit");
                    break;
                default:
                    Log?.Warning($"[Command] Unknown command: '{command}'. Type 'help' for available commands.");
                    break;
            }
        }

        // This utility function uses Console directly for interaction - Keep as is
        private static void CreateApiKeys()
        {
            try
            {
                Console.WriteLine("API Key Encryption Utility");
                Console.WriteLine("-------------------------");
                Console.Write("Enter file path: "); 
                string filePath = Console.ReadLine()?.Trim() ?? throw new InvalidOperationException("File path empty");
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory))
                    Directory.CreateDirectory(directory);
                Console.Write("Enter API Key: "); string apiKey = Console.ReadLine()?.Trim() ?? throw new InvalidOperationException("API key empty");
                Console.Write("Enter Secret Key: "); string secretKey = Console.ReadLine()?.Trim() ?? throw new InvalidOperationException("Secret key empty");

                ExchangeSharp.CryptoUtility.SaveUnprotectedStringsToFile(filePath, new[] { apiKey, secretKey });
                Console.WriteLine($"Keys encrypted and saved to: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating keys file: {ex.Message}");
            }
        }

        // This uses the passed-in TextWriter (_originalConsoleOut) - Keep as is
        private static async Task WaitForEnterBeforeExitAsync(string message, TextWriter targetConsole, int timeoutSeconds = 0)
        {
            targetConsole.WriteLine(message);
            if (Console.IsInputRedirected)
            {
                targetConsole.WriteLine("(Input is redirected, cannot wait for Enter)");
                if (timeoutSeconds > 0) 
                    await Task.Delay(TimeSpan.FromSeconds(timeoutSeconds));
                return;
            }

            try
            {
                var readKeyTask = Task.Run(() => Console.ReadKey(true));
                if (timeoutSeconds > 0)
                {
                    var timeoutTask = Task.Delay(TimeSpan.FromSeconds(timeoutSeconds));
                    await Task.WhenAny(readKeyTask, timeoutTask);
                    if (!readKeyTask.IsCompleted) 
                        targetConsole.WriteLine("(Timeout waiting for key)");
                }
                else 
                {
                    await readKeyTask;
                }
            }
            catch (InvalidOperationException ex)
            {
                targetConsole.WriteLine($"Warning: Cannot wait for key press ({ex.Message})");
            }
            catch (Exception ex)
            {
                targetConsole.WriteLine($"Warning: Error waiting for key press ({ex.Message})");
            }
        }
    }
}
